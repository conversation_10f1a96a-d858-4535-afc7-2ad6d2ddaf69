#!/bin/bash

# Deploy Lambda Proxy Function for CloudWatch Events
# This script creates and deploys a Lambda function that can proxy HTTP requests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
FUNCTION_NAME="cron-proxy-lambda"
AWS_REGION="us-east-1"
RUNTIME="nodejs18.x"
TIMEOUT=30
MEMORY_SIZE=128
DRY_RUN=false
VERBOSE=false

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy AWS Lambda function for CloudWatch Events HTTP proxy.

OPTIONS:
    -f, --function-name NAME   Lambda function name (default: cron-proxy-lambda)
    -r, --region REGION        AWS region (default: us-east-1)
    -t, --timeout SECONDS      Function timeout in seconds (default: 30)
    -m, --memory MB            Memory size in MB (default: 128)
    -d, --dry-run              Show what would be deployed without executing
    -v, --verbose              Enable verbose output
    -h, --help                 Show this help message

EXAMPLES:
    # Deploy with default settings
    $0

    # Deploy with custom function name and region
    $0 -f my-cron-proxy -r us-west-2

    # Dry run to see what would be deployed
    $0 -d

REQUIREMENTS:
    - AWS CLI installed and configured
    - Appropriate IAM permissions for Lambda

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--function-name)
            FUNCTION_NAME="$2"
            shift 2
            ;;
        -r|--region)
            AWS_REGION="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -m|--memory)
            MEMORY_SIZE="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check AWS CLI
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed or not in PATH"
    exit 1
fi

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS credentials not configured or invalid"
    exit 1
fi

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LAMBDA_DIR="$SCRIPT_DIR/lambda-proxy"

# Check if Lambda code exists
if [ ! -f "$LAMBDA_DIR/index.js" ]; then
    print_error "Lambda function code not found at: $LAMBDA_DIR/index.js"
    exit 1
fi

print_status "Deploying Lambda function: $FUNCTION_NAME"
print_debug "Region: $AWS_REGION"
print_debug "Runtime: $RUNTIME"
print_debug "Timeout: ${TIMEOUT}s"
print_debug "Memory: ${MEMORY_SIZE}MB"

if [ "$DRY_RUN" = true ]; then
    print_status "DRY RUN: Would deploy Lambda function with the above settings"
    exit 0
fi

# Create deployment package
print_status "Creating deployment package..."
cd "$LAMBDA_DIR"
zip -q lambda-function.zip index.js

# Get AWS account ID
ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

# Create IAM role for Lambda if it doesn't exist
ROLE_NAME="lambda-cron-proxy-role"
ROLE_ARN="arn:aws:iam::$ACCOUNT_ID:role/$ROLE_NAME"

if ! aws iam get-role --role-name "$ROLE_NAME" &>/dev/null; then
    print_status "Creating IAM role: $ROLE_NAME"
    
    # Create trust policy
    cat > trust-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "lambda.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
EOF
    
    # Create the role
    aws iam create-role \
        --role-name "$ROLE_NAME" \
        --assume-role-policy-document file://trust-policy.json \
        --description "Role for Lambda cron proxy function"
    
    # Attach basic execution policy
    aws iam attach-role-policy \
        --role-name "$ROLE_NAME" \
        --policy-arn "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
    
    # Clean up
    rm trust-policy.json
    
    print_status "Waiting for role to be available..."
    sleep 10
else
    print_debug "IAM role already exists: $ROLE_NAME"
fi

# Check if function exists
if aws lambda get-function --function-name "$FUNCTION_NAME" --region "$AWS_REGION" &>/dev/null; then
    print_status "Updating existing Lambda function: $FUNCTION_NAME"
    
    # Update function code
    aws lambda update-function-code \
        --function-name "$FUNCTION_NAME" \
        --zip-file fileb://lambda-function.zip \
        --region "$AWS_REGION"
    
    # Update function configuration
    aws lambda update-function-configuration \
        --function-name "$FUNCTION_NAME" \
        --runtime "$RUNTIME" \
        --timeout "$TIMEOUT" \
        --memory-size "$MEMORY_SIZE" \
        --region "$AWS_REGION"
        
else
    print_status "Creating new Lambda function: $FUNCTION_NAME"
    
    # Create function
    aws lambda create-function \
        --function-name "$FUNCTION_NAME" \
        --runtime "$RUNTIME" \
        --role "$ROLE_ARN" \
        --handler "index.handler" \
        --zip-file fileb://lambda-function.zip \
        --timeout "$TIMEOUT" \
        --memory-size "$MEMORY_SIZE" \
        --description "Proxy function for CloudWatch Events to HTTP endpoints" \
        --region "$AWS_REGION"
fi

# Clean up
rm lambda-function.zip

print_status "Lambda function deployed successfully!"
print_status "Function name: $FUNCTION_NAME"
print_status "Function ARN: arn:aws:lambda:$AWS_REGION:$ACCOUNT_ID:function:$FUNCTION_NAME"

print_status "You can now use this function with the CloudWatch Events setup script:"
echo "  ./setup-cloudwatch-cron-events-lambda.sh -e prod -u https://api.example.com -t token -l $FUNCTION_NAME"
