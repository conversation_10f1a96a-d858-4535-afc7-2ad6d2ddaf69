# CloudWatch Events Cron Job Setup

This directory contains scripts and configuration for setting up AWS CloudWatch Events to replace the distributed cron jobs that were previously managed by the application.

## Overview

The application previously used PM2 cluster mode with Redis-based distributed locking (Redlock) to ensure cron jobs ran only once across multiple instances. This approach has been replaced with:

1. **REST API Endpoints**: All cron job logic has been converted to HTTP POST endpoints protected by `InternalTokenGuard`
2. **AWS CloudWatch Events**: External scheduling using AWS CloudWatch Events rules that trigger the API endpoints
3. **Simplified Architecture**: No more distributed locking complexity or PM2 cluster coordination

## Files

- `setup-cloudwatch-cron-events.sh` - Main setup script for creating CloudWatch Events rules
- `cleanup-cloudwatch-events.sh` - Script to remove all created CloudWatch Events rules
- `README.md` - This documentation file

## Prerequisites

### AWS Requirements
- AWS CLI installed and configured
- AWS credentials with appropriate permissions
- IAM role for CloudWatch Events to invoke HTTP endpoints

### Required IAM Permissions
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "events:PutRule",
                "events:PutTargets",
                "events:DeleteRule",
                "events:RemoveTargets",
                "events:ListRules",
                "events:ListTargetsByRule",
                "events:DescribeRule"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "iam:PassRole"
            ],
            "Resource": "arn:aws:iam::*:role/CloudWatchEventsRole"
        }
    ]
}
```

### Application Requirements
- Internal API token for authentication
- API endpoints deployed and accessible
- `InternalTokenGuard` properly configured

## Usage

### Setup CloudWatch Events

```bash
# Basic usage
./setup-cloudwatch-cron-events.sh \
  --environment prod \
  --api-url https://api.propaga.io \
  --token sk_oinyo7bt9863bt498374ry0b \
  --dry-run \
  --verbose

# With custom AWS region
./setup-cloudwatch-cron-events.sh \
  --environment staging \
  --api-url https://api-staging.example.com \
  --token your-token \
  --region us-west-2

# Dry run to see what would be created
./setup-cloudwatch-cron-events.sh \
  --environment dev \
  --api-url https://api-dev.example.com \
  --token your-token \
  --dry-run

# Verbose output for debugging
./setup-cloudwatch-cron-events.sh \
  --environment prod \
  --api-url https://api.example.com \
  --token your-token \
  --verbose
```

### Cleanup CloudWatch Events

```bash
# Remove all rules for an environment
./cleanup-cloudwatch-events.sh --environment prod

# Dry run cleanup
./cleanup-cloudwatch-events.sh --environment staging --dry-run
```

## Converted Endpoints

### Transaction Controller (12 endpoints)
- `POST /cron-transaction/transaction-status-update` - Daily at 1AM
- `POST /cron-transaction/wholesaler-transfer/rabbit` - Mon-Sat at 8AM
- `POST /cron-transaction/wholesaler-transfer/chiper` - Mon-Sat at 7AM
- `POST /cron-transaction/wholesaler-transfer/rintin` - Mon-Sat at 6AM
- `POST /cron-transaction/wholesaler-transfer/nestle` - Mon-Sat at 6AM
- `POST /cron-transaction/wholesaler-transfer/mercanto` - Mon-Sat at 6AM
- `POST /cron-transaction/wholesaler-transfer/surtace` - Mon-Sat at 7AM
- `POST /cron-transaction/wholesaler-transfer/scorpion` - Mon-Sat at 6AM
- `POST /cron-transaction/wholesaler-transfer/helados-holanda` - Mon-Sat at 6AM
- `POST /cron-transaction/cancel-pending-transactions` - Every 30 minutes
- `POST /cron-transaction/create-payment-plan-ticket` - Every 15 minutes
- `POST /cron-transaction/cancel-ghost-transactions` - Daily at 7PM
- `POST /cron-transaction/create-automatic-receipts` - Daily at midnight

### Notification Controller (7 endpoints)
- `POST /cron-notification/unused-credit-notification` - Manual trigger
- `POST /cron-notification/payment-day-notification` - Every 5 minutes
- `POST /cron-notification/second-payment-day-notification` - Daily at 1PM
- `POST /cron-notification/third-payment-day-notification` - Daily at 6PM
- `POST /cron-notification/in-default-user-notification` - Manual trigger
- `POST /cron-notification/payment-expires-soon-notification` - Daily at 11AM
- `POST /cron-notification/payment-plan-ticket-notification` - Daily at noon
- `POST /cron-notification/credit-limit-update-notification` - Mon-Fri at 11AM
- `POST /cron-notification/delivery-word-notification/rabbit` - Daily at 7:30AM

### Revenue Share Controller (2 endpoints)
- `POST /cron-revenue-share/revenue-share-transfer/rabbit` - 1st day of month
- `POST /cron-revenue-share/revenue-share-transfer/chiper` - 1st day of month

### Corner Store Controller (2 endpoints)
- `POST /cron-corner-store/update-search-client-prod` - Every 25 minutes
- `POST /cron-corner-store/update-search-client-dev` - Daily at midnight

## Schedule Formats

CloudWatch Events uses different schedule expression formats:

### Cron Expressions
```
cron(Minutes Hours Day-of-month Month Day-of-week Year)
```

Examples:
- `cron(0 1 * * ? *)` - Daily at 1AM
- `cron(0 8 ? * MON-SAT *)` - Monday to Saturday at 8AM
- `cron(0 0 1 * ? *)` - First day of every month at midnight

### Rate Expressions
```
rate(value unit)
```

Examples:
- `rate(5 minutes)` - Every 5 minutes
- `rate(1 hour)` - Every hour
- `rate(1 day)` - Every day

## Troubleshooting

### Common Issues

1. **Permission Denied**
   - Ensure AWS credentials are configured
   - Verify IAM permissions for CloudWatch Events

2. **Rule Creation Failed**
   - Check AWS region settings
   - Verify rule name doesn't already exist
   - Ensure schedule expression is valid

3. **Target Addition Failed**
   - Verify CloudWatchEventsRole exists
   - Check HTTP endpoint URL is accessible
   - Ensure internal token is valid

### Debugging

Use the `--verbose` flag to see detailed output:
```bash
./setup-cloudwatch-cron-events.sh --environment dev --api-url https://api.com --token token --verbose
```

Use `--dry-run` to test without creating resources:
```bash
./setup-cloudwatch-cron-events.sh --environment dev --api-url https://api.com --token token --dry-run
```

## Environment-Specific Considerations

### Development
- Use separate rule names with `dev-` prefix
- Consider using longer intervals for testing
- May want to disable some rules that are production-only

### Staging
- Mirror production schedules
- Use `staging-` prefix for rule names
- Test with production-like data volumes

### Production
- Use exact schedules from original cron jobs
- Monitor CloudWatch metrics for rule execution
- Set up alerts for failed invocations

## Migration Checklist

- [ ] Deploy application with converted REST endpoints
- [ ] Verify `InternalTokenGuard` is working
- [ ] Test endpoints manually with internal token
- [ ] Run setup script in dry-run mode
- [ ] Create CloudWatch Events rules
- [ ] Monitor first few executions
- [ ] Disable old PM2 cron jobs
- [ ] Remove Redlock dependencies from application
- [ ] Update deployment documentation

## Monitoring

After setup, monitor the following:

1. **CloudWatch Events Console**
   - Rule execution metrics
   - Failed invocation alerts
   - Target health

2. **Application Logs**
   - Endpoint execution logs
   - Error rates
   - Performance metrics

3. **API Metrics**
   - Response times
   - Success/failure rates
   - Authentication errors
