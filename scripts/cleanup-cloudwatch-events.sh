#!/bin/bash

# AWS CloudWatch Events Cleanup Script
# This script removes all CloudWatch Events rules created by the setup script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=""
AWS_REGION="us-east-1"
DRY_RUN=false
VERBOSE=false
FORCE=false

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Remove AWS CloudWatch Events rules created for cron job endpoints.

OPTIONS:
    -e, --environment ENV       Environment (dev, staging, prod) [REQUIRED]
    -r, --region REGION        AWS region (default: us-east-1)
    -d, --dry-run              Show what would be deleted without executing
    -f, --force                Skip confirmation prompts
    -v, --verbose              Enable verbose output
    -h, --help                 Show this help message

EXAMPLES:
    # Remove all rules for development environment
    $0 -e dev

    # Remove rules with confirmation
    $0 -e prod -f

    # Dry run to see what would be deleted
    $0 -e staging -d

REQUIREMENTS:
    - AWS CLI installed and configured
    - Appropriate IAM permissions for CloudWatch Events

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -r|--region)
            AWS_REGION="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [ -z "$ENVIRONMENT" ]; then
    print_error "Environment is required. Use -e or --environment flag."
    exit 1
fi

# Validate environment
case $ENVIRONMENT in
    dev|staging|prod)
        ;;
    *)
        print_error "Invalid environment: $ENVIRONMENT. Must be one of: dev, staging, prod"
        exit 1
        ;;
esac

# Check AWS CLI
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed or not in PATH"
    exit 1
fi

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS credentials not configured or invalid"
    exit 1
fi

print_status "Starting CloudWatch Events cleanup for environment: $ENVIRONMENT"
print_debug "AWS Region: $AWS_REGION"
print_debug "Dry Run: $DRY_RUN"

# Get all rules for the environment
RULE_PREFIX="${ENVIRONMENT}-"
print_debug "Looking for rules with prefix: $RULE_PREFIX"

# List all rules that match our environment prefix
RULES=$(aws events list-rules \
    --region "$AWS_REGION" \
    --name-prefix "$RULE_PREFIX" \
    --query 'Rules[].Name' \
    --output text)

if [ -z "$RULES" ]; then
    print_warning "No CloudWatch Events rules found for environment: $ENVIRONMENT"
    exit 0
fi

# Convert to array
RULES_ARRAY=($RULES)
RULE_COUNT=${#RULES_ARRAY[@]}

print_status "Found $RULE_COUNT rules to delete:"
for rule in "${RULES_ARRAY[@]}"; do
    echo "  - $rule"
done

# Confirmation prompt (unless force or dry-run)
if [ "$DRY_RUN" = false ] && [ "$FORCE" = false ]; then
    echo
    read -p "Are you sure you want to delete these $RULE_COUNT rules? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Operation cancelled."
        exit 0
    fi
fi

# Function to delete a CloudWatch rule
delete_cloudwatch_rule() {
    local rule_name="$1"
    
    print_debug "Deleting rule: $rule_name"
    
    if [ "$DRY_RUN" = true ]; then
        echo "DRY RUN: Would delete rule '$rule_name'"
        return 0
    fi
    
    # First, remove all targets from the rule
    local targets=$(aws events list-targets-by-rule \
        --region "$AWS_REGION" \
        --rule "$rule_name" \
        --query 'Targets[].Id' \
        --output text)
    
    if [ -n "$targets" ]; then
        print_debug "Removing targets from rule: $rule_name"
        aws events remove-targets \
            --region "$AWS_REGION" \
            --rule "$rule_name" \
            --ids $targets
        
        if [ $? -eq 0 ]; then
            print_debug "Removed targets from rule: $rule_name"
        else
            print_error "Failed to remove targets from rule: $rule_name"
            return 1
        fi
    fi
    
    # Then delete the rule
    aws events delete-rule \
        --region "$AWS_REGION" \
        --name "$rule_name"
    
    if [ $? -eq 0 ]; then
        print_status "Deleted rule: $rule_name"
    else
        print_error "Failed to delete rule: $rule_name"
        return 1
    fi
}

# Delete each rule
DELETED_COUNT=0
FAILED_COUNT=0

for rule in "${RULES_ARRAY[@]}"; do
    if delete_cloudwatch_rule "$rule"; then
        ((DELETED_COUNT++))
    else
        ((FAILED_COUNT++))
    fi
done

# Summary
echo
if [ "$DRY_RUN" = true ]; then
    print_status "Dry run completed. Would have deleted $RULE_COUNT rules."
else
    print_status "Cleanup completed!"
    print_status "Successfully deleted: $DELETED_COUNT rules"
    if [ $FAILED_COUNT -gt 0 ]; then
        print_warning "Failed to delete: $FAILED_COUNT rules"
        exit 1
    fi
fi
