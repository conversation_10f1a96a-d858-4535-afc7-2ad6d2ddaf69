#!/bin/bash

# AWS CloudWatch Events Setup Script for Cron Job Endpoints
# This script creates CloudWatch Events rules to trigger REST API endpoints
# that replace the original cron jobs in the application.

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${SCRIPT_DIR}/cloudwatch-config.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=""
API_BASE_URL=""
INTERNAL_TOKEN=""
AWS_REGION="us-east-1"
DRY_RUN=false
VERBOSE=false

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Setup AWS CloudWatch Events rules for cron job endpoints.

OPTIONS:
    -e, --environment ENV       Environment (dev, staging, prod) [REQUIRED]
    -u, --api-url URL          Base API URL [REQUIRED]
    -t, --token TOKEN          Internal API token [REQUIRED]
    -r, --region REGION        AWS region (default: us-east-1)
    -d, --dry-run              Show what would be created without executing
    -v, --verbose              Enable verbose output
    -h, --help                 Show this help message

EXAMPLES:
    # Setup for development environment
    $0 -e dev -u https://api-dev.example.com -t your-internal-token

    # Setup for production with custom region
    $0 -e prod -u https://api.example.com -t your-token -r us-west-2

    # Dry run to see what would be created
    $0 -e staging -u https://api-staging.example.com -t token -d

ENVIRONMENT VARIABLES:
    AWS_REGION                 AWS region (overridden by -r flag)
    API_BASE_URL              Base API URL (overridden by -u flag)
    INTERNAL_TOKEN            Internal API token (overridden by -t flag)

REQUIREMENTS:
    - AWS CLI installed and configured
    - Appropriate IAM permissions for CloudWatch Events
    - Valid internal API token for the target environment

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -u|--api-url)
            API_BASE_URL="$2"
            shift 2
            ;;
        -t|--token)
            INTERNAL_TOKEN="$2"
            shift 2
            ;;
        -r|--region)
            AWS_REGION="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [ -z "$ENVIRONMENT" ]; then
    print_error "Environment is required. Use -e or --environment flag."
    exit 1
fi

if [ -z "$API_BASE_URL" ]; then
    print_error "API base URL is required. Use -u or --api-url flag."
    exit 1
fi

if [ -z "$INTERNAL_TOKEN" ]; then
    print_error "Internal token is required. Use -t or --token flag."
    exit 1
fi

# Validate environment
case $ENVIRONMENT in
    dev|staging|prod)
        ;;
    *)
        print_error "Invalid environment: $ENVIRONMENT. Must be one of: dev, staging, prod"
        exit 1
        ;;
esac

# Check AWS CLI
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed or not in PATH"
    exit 1
fi

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS credentials not configured or invalid"
    exit 1
fi

print_status "Starting CloudWatch Events setup for environment: $ENVIRONMENT"
print_debug "API Base URL: $API_BASE_URL"
print_debug "AWS Region: $AWS_REGION"
print_debug "Dry Run: $DRY_RUN"

# Function to create CloudWatch rule
create_cloudwatch_rule() {
    local rule_name="$1"
    local schedule="$2"
    local description="$3"
    local endpoint="$4"
    local method="POST"
    
    local full_rule_name="${ENVIRONMENT}-${rule_name}"
    local target_url="${API_BASE_URL}${endpoint}"
    
    print_debug "Creating rule: $full_rule_name"
    print_debug "Schedule: $schedule"
    print_debug "Target URL: $target_url"
    
    if [ "$DRY_RUN" = true ]; then
        echo "DRY RUN: Would create rule '$full_rule_name' with schedule '$schedule' targeting '$target_url'"
        return 0
    fi
    
    # Create the rule
    aws events put-rule \
        --region "$AWS_REGION" \
        --name "$full_rule_name" \
        --schedule-expression "$schedule" \
        --description "$description" \
        --state ENABLED
    
    if [ $? -eq 0 ]; then
        print_status "Created rule: $full_rule_name"
    else
        print_error "Failed to create rule: $full_rule_name"
        return 1
    fi
    
    # Create HTTP target
    local target_id="${full_rule_name}-target"
    local targets_json=$(cat << EOF
[
    {
        "Id": "$target_id",
        "Arn": "arn:aws:events:$AWS_REGION:$(aws sts get-caller-identity --query Account --output text):destination/http",
        "HttpParameters": {
            "HeaderParameters": {
                "Authorization": "$INTERNAL_TOKEN",
                "Content-Type": "application/json"
            },
            "PathParameterValues": {},
            "QueryStringParameters": {}
        },
        "RoleArn": "arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):role/CloudWatchEventsRole"
    }
]
EOF
)
    
    # Add target to rule
    aws events put-targets \
        --region "$AWS_REGION" \
        --rule "$full_rule_name" \
        --targets "$targets_json"
    
    if [ $? -eq 0 ]; then
        print_status "Added target to rule: $full_rule_name"
    else
        print_error "Failed to add target to rule: $full_rule_name"
        return 1
    fi
}

# Cron job definitions
# Format: rule_name|schedule|description|endpoint

CRON_JOBS=(
    # Transaction Controller Jobs
    "transaction-status-update|cron(0 1 * * ? *)|Daily transaction status update|/cron-transaction/transaction-status-update"
    "wholesaler-transfer-rabbit|cron(0 8 ? * MON-SAT *)|Wholesaler transfer for Rabbit (Mon-Sat 8AM)|/cron-transaction/wholesaler-transfer/rabbit"
    "wholesaler-transfer-chiper|cron(0 7 ? * MON-SAT *)|Wholesaler transfer for Chiper (Mon-Sat 7AM)|/cron-transaction/wholesaler-transfer/chiper"
    "wholesaler-transfer-rintin|cron(0 6 ? * MON-SAT *)|Wholesaler transfer for Rintin (Mon-Sat 6AM)|/cron-transaction/wholesaler-transfer/rintin"
    "wholesaler-transfer-nestle|cron(0 6 ? * MON-SAT *)|Wholesaler transfer for Nestle (Mon-Sat 6AM)|/cron-transaction/wholesaler-transfer/nestle"
    "wholesaler-transfer-mercanto|cron(0 6 ? * MON-SAT *)|Wholesaler transfer for Mercanto (Mon-Sat 6AM)|/cron-transaction/wholesaler-transfer/mercanto"
    "wholesaler-transfer-surtace|cron(0 7 ? * MON-SAT *)|Wholesaler transfer for Surtace (Mon-Sat 7AM)|/cron-transaction/wholesaler-transfer/surtace"
    "wholesaler-transfer-scorpion|cron(0 6 ? * MON-SAT *)|Wholesaler transfer for Scorpion (Mon-Sat 6AM)|/cron-transaction/wholesaler-transfer/scorpion"
    "wholesaler-transfer-helados-holanda|cron(0 6 ? * MON-SAT *)|Wholesaler transfer for Helados Holanda (Mon-Sat 6AM)|/cron-transaction/wholesaler-transfer/helados-holanda"
    "cancel-pending-transactions|rate(30 minutes)|Cancel pending transactions every 30 minutes|/cron-transaction/cancel-pending-transactions"
    "create-payment-plan-ticket|rate(15 minutes)|Create payment plan tickets every 15 minutes|/cron-transaction/create-payment-plan-ticket"
    "cancel-ghost-transactions|cron(0 19 * * ? *)|Cancel ghost transactions daily at 7PM|/cron-transaction/cancel-ghost-transactions"
    "create-automatic-receipts|cron(0 0 * * ? *)|Create automatic receipts daily at midnight|/cron-transaction/create-automatic-receipts"
    
    # Notification Controller Jobs
    "unused-credit-notification|rate(1 day)|Unused credit notification (manual trigger)|/cron-notification/unused-credit-notification"
    "payment-day-notification|rate(5 minutes)|Payment day notification every 5 minutes|/cron-notification/payment-day-notification"
    "second-payment-day-notification|cron(0 13 * * ? *)|Second payment day notification daily at 1PM|/cron-notification/second-payment-day-notification"
    "third-payment-day-notification|cron(0 18 * * ? *)|Third payment day notification daily at 6PM|/cron-notification/third-payment-day-notification"
    "in-default-user-notification|rate(1 day)|In default user notification (manual trigger)|/cron-notification/in-default-user-notification"
    "payment-expires-soon-notification|cron(0 11 * * ? *)|Payment expires soon notification daily at 11AM|/cron-notification/payment-expires-soon-notification"
    "payment-plan-ticket-notification|cron(0 12 * * ? *)|Payment plan ticket notification daily at noon|/cron-notification/payment-plan-ticket-notification"
    "credit-limit-update-notification|cron(0 11 ? * MON-FRI *)|Credit limit update notification (Mon-Fri 11AM)|/cron-notification/credit-limit-update-notification"
    "delivery-word-notification-rabbit|cron(30 7 * * ? *)|Delivery word notification for Rabbit daily at 7:30AM|/cron-notification/delivery-word-notification/rabbit"
    
    # Revenue Share Controller Jobs
    "revenue-share-transfer-rabbit|cron(0 0 1 * ? *)|Revenue share transfer for Rabbit (1st day of month)|/cron-revenue-share/revenue-share-transfer/rabbit"
    "revenue-share-transfer-chiper|cron(0 0 1 * ? *)|Revenue share transfer for Chiper (1st day of month)|/cron-revenue-share/revenue-share-transfer/chiper"
    
    # Corner Store Controller Jobs
    "update-search-client-prod|rate(25 minutes)|Update search client for production every 25 minutes|/cron-corner-store/update-search-client-prod"
    "update-search-client-dev|cron(0 0 * * ? *)|Update search client for development daily at midnight|/cron-corner-store/update-search-client-dev"
)

print_status "Creating ${#CRON_JOBS[@]} CloudWatch Events rules..."

# Create each cron job
for job in "${CRON_JOBS[@]}"; do
    IFS='|' read -r rule_name schedule description endpoint <<< "$job"
    
    if ! create_cloudwatch_rule "$rule_name" "$schedule" "$description" "$endpoint"; then
        print_error "Failed to create rule for: $rule_name"
        exit 1
    fi
done

if [ "$DRY_RUN" = true ]; then
    print_status "Dry run completed. No actual resources were created."
else
    print_status "Successfully created all CloudWatch Events rules for environment: $ENVIRONMENT"
fi

print_status "Setup completed!"
