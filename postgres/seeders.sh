# /bin/bash

echo 'Running seeders'

echo 'running initial seeders'

docker exec -i propaga_database psql -U leo -d propaga  < ./postgres/seeders/initial.sql

echo 'running merge account seeders'

docker exec -i propaga_database psql -U leo -d propaga  < ./postgres/seeders/merge-account.sql

echo 'running transaction by batch seeders'

docker exec -i propaga_database psql -U leo -d propaga  < ./postgres/seeders/transaction-by-batch.sql

echo 'running kyc seeders'

docker exec -i propaga_database psql -U leo -d propaga  < ./postgres/seeders/onboarding.sql

echo 'running transaction wholesalers validations seeders'

docker exec -i propaga_database psql -U leo -d propaga  < ./postgres/seeders/transaction-wholesalers-validations.sql

echo 'running phone number sanitization seeders'

docker exec -i propaga_database psql -U leo -d propaga  < ./postgres/seeders/phone-number-sanitization.sql

echo 'running deposit seeders'
docker exec -i propaga_database psql -U leo -d propaga  < ./postgres/seeders/deposit.sql

echo 'running blocked users seeders'
docker exec -i propaga_database psql -U leo -d propaga  < ./postgres/seeders/blocked-users.sql

echo 'finished successfull!'