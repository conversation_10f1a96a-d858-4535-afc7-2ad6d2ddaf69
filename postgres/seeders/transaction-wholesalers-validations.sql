--------------------------------------
-----CornerStore----------------------
--------------------------------------
INSERT INTO "Propaga"."CornerStore" (
    id,
    "cornerStoreName",
    "creditLimit",
    address,
    verified,
    "createdAt",
    "updatedAt",
    "location"
  )
VALUES (
    '74c9ac23-1542-4ca1-9e07-334cbcf34cbb',
    'Rabbit completed user',
    50000,
    'Av 123',
    DEFAULT,
    DEFAULT,
    DEFAULT,
    '{"latitud": "", "longitude": ""}'
  ),
  (
    '5c65d3cf-9b51-4bea-a4ed-d7d870b0442c',
    'Rabbit partial user',
    50000,
    'Av 123',
    DEFAULT,
    DEFAULT,
    DEFAULT,
    '{"latitud": "", "longitude": ""}'
  ),
  (
    'f80e2f0e-11f3-41f4-a0ad-6d27e7ca9781',
    'Rabbit partial user with active transaction',
    50000,
    'Av 123',
    DEFAULT,
    DEFAULT,
    DEFAULT,
    '{"latitud": "", "longitude": ""}'
  ),
  (
    '6125ccbc-4242-4cfd-a207-757f86578bc5',
    'Rabbit completed with multiple transactions user',
    50000,
    'Av 123',
    DEFAULT,
    DEFAULT,
    DEFAULT,
    '{"latitud": "", "longitude": ""}'
  ),
  (
    '5fb5515a-7aec-4ae4-833b-32e8d4dc2da3',
    'Chiper user',
    50000,
    'Av 123',
    DEFAULT,
    DEFAULT,
    DEFAULT,
    '{"latitud": "", "longitude": ""}'
  ),
  (
    '4872771f-b1da-4990-bcc0-0215a2879dcb',
    'Chiper user with multiple orders',
    50000,
    'Av 123',
    DEFAULT,
    DEFAULT,
    DEFAULT,
    '{"latitud": "", "longitude": ""}'
  );
--------------------------------------
-----Users----------------------------
--------------------------------------
INSERT INTO "Propaga"."Users" (
    id,
    "phoneNumber",
    "firstName",
    "lastNames",
    "email",
    "cornerStoreId",
    "createdAt",
    "updatedAt",
    "statusId",
    "dataVerification",
    "signUpStatusId"
  )
VALUES -- signUp completed user
  (
    'abe7c74a-4882-4ab9-9536-2196f8190b8c',
    '8645306566',
    NULL,
    NULL,
    NULL,
    '74c9ac23-1542-4ca1-9e07-334cbcf34cbb',
    DEFAULT,
    DEFAULT,
    '528a3aaa-0a1e-4d50-8f34-1def18a56394',
    NULL,
    '87755cf0-a1d9-4e0d-a950-b4498065a284' -- completed
  ),
  (
    -- signUp partial user
    'f0e9115a-0e5b-4786-a1d9-fcd9f97eaf6c',
    '3758213650',
    NULL,
    NULL,
    NULL,
    '5c65d3cf-9b51-4bea-a4ed-d7d870b0442c',
    DEFAULT,
    DEFAULT,
    '528a3aaa-0a1e-4d50-8f34-1def18a56394',
    NULL,
    '671f6cf6-ed0d-4a96-9e6f-06e0d9d3781f' -- partial
  ),
  (
    -- signUp completed user with multiple transactions
    '7db9e138-8306-4b55-a51b-64c5cd474543',
    '3758213651',
    NULL,
    NULL,
    NULL,
    '6125ccbc-4242-4cfd-a207-757f86578bc5',
    DEFAULT,
    DEFAULT,
    '528a3aaa-0a1e-4d50-8f34-1def18a56394',
    NULL,
    NULL -- signUpStatusId
  ),
  (
    -- signUp partial user with active transaction
    '59920481-9cff-4e58-8b41-11a6fa0198e5',
    '3758213651',
    NULL,
    NULL,
    NULL,
    'f80e2f0e-11f3-41f4-a0ad-6d27e7ca9781',
    DEFAULT,
    DEFAULT,
    '528a3aaa-0a1e-4d50-8f34-1def18a56394',
    NULL,
    '671f6cf6-ed0d-4a96-9e6f-06e0d9d3781f' -- partial
  ),
  (
    -- Chiper user
    '80e275e4-e8ed-4974-b6fd-90abaa350de4',
    '3837732896',
    NULL,
    NULL,
    NULL,
    '5fb5515a-7aec-4ae4-833b-32e8d4dc2da3',
    DEFAULT,
    DEFAULT,
    '528a3aaa-0a1e-4d50-8f34-1def18a56394',
    NULL,
    '87755cf0-a1d9-4e0d-a950-b4498065a284' -- completed
  ),
  (
    -- Chiper user with multiple transactions
    'baafce07-d457-45f7-8297-7d3c21f35dae',
    '3837732898',
    NULL,
    NULL,
    NULL,
    '4872771f-b1da-4990-bcc0-0215a2879dcb',
    DEFAULT,
    DEFAULT,
    '528a3aaa-0a1e-4d50-8f34-1def18a56394',
    NULL,
    '87755cf0-a1d9-4e0d-a950-b4498065a284' -- completed
  );
--------------------------------------
-----WholesalerInformation------------
--------------------------------------
INSERT INTO "Propaga"."WholesalerInformation" (
    id,
    "wholesalerId",
    "statusId",
    "externalId",
    data,
    "userId",
    "createdAt",
    "updatedAt",
    "userFlags"
  )
VALUES (
    -- singUp completed user
    DEFAULT,
    -- Rabbit
    '94ea9c62-bc04-442d-b91c-6d4a5e9ed0bc',
    '1393b08f-2483-4d53-b1de-2bbb93c78af0',
    '16340',
    '{}',
    'abe7c74a-4882-4ab9-9536-2196f8190b8c',
    DEFAULT,
    DEFAULT,
    '{ "is_presale_user": false }'
  ),
  (
    -- signUp partial user
    DEFAULT,
    -- Rabbit
    '94ea9c62-bc04-442d-b91c-6d4a5e9ed0bc',
    '1393b08f-2483-4d53-b1de-2bbb93c78af0',
    '16341',
    '{}',
    'f0e9115a-0e5b-4786-a1d9-fcd9f97eaf6c',
    DEFAULT,
    DEFAULT,
    '{ "is_presale_user": false }'
  ),
  (
    -- signUp partial user with active transaction
    DEFAULT,
    -- Rabbit
    '94ea9c62-bc04-442d-b91c-6d4a5e9ed0bc',
    '1393b08f-2483-4d53-b1de-2bbb93c78af0',
    '16341',
    '{}',
    '59920481-9cff-4e58-8b41-11a6fa0198e5',
    DEFAULT,
    DEFAULT,
    '{ "is_presale_user": false }'
  ),
  (
    -- singUp completed user with multiple transactions
    DEFAULT,
    -- Rabbit
    '94ea9c62-bc04-442d-b91c-6d4a5e9ed0bc',
    '1393b08f-2483-4d53-b1de-2bbb93c78af0',
    '16341',
    '{}',
    '7db9e138-8306-4b55-a51b-64c5cd474543',
    DEFAULT,
    DEFAULT,
    '{ "is_presale_user": false }'
  ),
  (
    -- Chiper user
    DEFAULT,
    -- Chiper
    'b9e21eec-232f-4b1f-9ab1-b9171c7edda9',
    '1393b08f-2483-4d53-b1de-2bbb93c78af0',
    '898989',
    '{}',
    '80e275e4-e8ed-4974-b6fd-90abaa350de4',
    DEFAULT,
    DEFAULT,
    '{ "is_presale_user": false }'
  ),
  (
    -- Chiper user with multiple transactions
    DEFAULT,
    -- Chiper
    'b9e21eec-232f-4b1f-9ab1-b9171c7edda9',
    '1393b08f-2483-4d53-b1de-2bbb93c78af0',
    '898989',
    '{}',
    'baafce07-d457-45f7-8297-7d3c21f35dae',
    DEFAULT,
    DEFAULT,
    '{ "is_presale_user": false }'
  ),
  (
    -- Chiper user merged with a rabbit partial signup user
    DEFAULT,
    -- Chiper
    'b9e21eec-232f-4b1f-9ab1-b9171c7edda9',
    '1393b08f-2483-4d53-b1de-2bbb93c78af0',
    '898989',
    '{}',
    '59920481-9cff-4e58-8b41-11a6fa0198e5',
    DEFAULT,
    DEFAULT,
    '{ "is_presale_user": false }'
  ),
  (
    -- Chiper user merged with a rabbit completed signup user
    DEFAULT,
    -- Chiper
    'b9e21eec-232f-4b1f-9ab1-b9171c7edda9',
    '1393b08f-2483-4d53-b1de-2bbb93c78af0',
    '898989',
    '{}',
    'abe7c74a-4882-4ab9-9536-2196f8190b8c',
    DEFAULT,
    DEFAULT,
    '{ "is_presale_user": false }'
  );
--------------------------------------
-----Transaction----------------------
--------------------------------------
INSERT INTO "Propaga"."Transaction" (
    id,
    "totalAmount",
    "movementDate",
    "wholesalerId",
    "cornerStoreId",
    "statusId",
    interests,
    "totalAmountWithInterests",
    "wholesalerTransactionId"
  )
VALUES (
    DEFAULT,
    2000,
    '2023-01-01T00:00:00.000Z',
    '94ea9c62-bc04-442d-b91c-6d4a5e9ed0bc',
    '6125ccbc-4242-4cfd-a207-757f86578bc5',
    -- on-hold
    'bcbd7430-3c94-42d1-86be-e6d93b6149f0',
    30,
    2030,
    'PROP123'
  ),
  (
    DEFAULT,
    2000,
    '2023-01-01T00:00:00.000Z',
    '94ea9c62-bc04-442d-b91c-6d4a5e9ed0bc',
    'f80e2f0e-11f3-41f4-a0ad-6d27e7ca9781',
    -- on-hold
    'bcbd7430-3c94-42d1-86be-e6d93b6149f0',
    30,
    2030,
    'PROP123'
  ),
  (
    DEFAULT,
    2000,
    '2023-01-01T00:00:00.000Z',
    -- Chiper
    'b9e21eec-232f-4b1f-9ab1-b9171c7edda9',
    '4872771f-b1da-4990-bcc0-0215a2879dcb',
    -- on-hold
    'bcbd7430-3c94-42d1-86be-e6d93b6149f0',
    30,
    2030,
    'PROP123'
  ),
  (
    DEFAULT,
    2000,
    '2023-01-01T00:00:00.000Z',
    -- Chiper
    'b9e21eec-232f-4b1f-9ab1-b9171c7edda9',
    '4872771f-b1da-4990-bcc0-0215a2879dcb',
    -- on-hold
    'bcbd7430-3c94-42d1-86be-e6d93b6149f0',
    30,
    2030,
    'PROP123'
  ),
  (
    DEFAULT,
    2000,
    '2023-01-01T00:00:00.000Z',
    -- Chiper
    'b9e21eec-232f-4b1f-9ab1-b9171c7edda9',
    '4872771f-b1da-4990-bcc0-0215a2879dcb',
    -- on-hold
    'bcbd7430-3c94-42d1-86be-e6d93b6149f0',
    30,
    2030,
    'PROP123'
  );