INSERT INTO "Propaga"."CornerStore" (
        id,
        "cornerStoreName",
        "creditLimit",
        address,
        verified,
        "createdAt",
        "updatedAt",
        "location"
    )
VALUES 
    -- current user:
    (
        'b8b09849-4ff5-42e0-aa6f-6ad545892e4d',
        'papitas',
        50000,
        'Av 123',
        DEFAULT,
        DEFAULT,
        DEFAULT,
        '{"latitud": "", "longitude": ""}'
    ),
    -- new user:
    (
        'ea191c5a-3cb4-40b8-a43a-0fee74ea87c7',
        'waldos',
        50000,
        'Av 123',
        DEFAULT,
        DEFAULT,
        DEFAULT,
        '{"latitud": "", "longitude": ""}'
    ),
    -- user in payment default:
    (
        'e0e319f1-380e-4400-bbe0-5665ec489cce',
        'papitas',
        50000,
        'Av 123',
        DEFAULT,
        DEFAULT,
        DEFAULT,
        '{"latitud": "", "longitude": ""}'
    ),
    -- new user register to payment default user:
    (
        '1e255912-bfde-4543-8fec-a66ab7b6ae6c',
        'waldos',
        50000,
        'Av 123',
        DEFAULT,
        DEFAULT,
        DEFAULT,
        '{"latitud": "", "longitude": ""}'
    )
;

INSERT INTO "Propaga"."Users" (
        id,
        "phoneNumber",
        "firstName",
        "lastNames",
        "email",
        "cornerStoreId",
        "createdAt",
        "updatedAt",
        "statusId",
        "dataVerification"
    )
VALUES  
    -- current user: 
    (
       '6e2b1a52-b039-4b5d-914f-7066c9daff2a',
       '1232222244',
        'Juan',
        'Perez',
        '<EMAIL>',
        'b8b09849-4ff5-42e0-aa6f-6ad545892e4d',
        DEFAULT,
        DEFAULT,
        '528a3aaa-0a1e-4d50-8f34-1def18a56394',
        '{"contacts":[{"fullName":". Mama","phoneNumber":"+444444"},{"fullName":"Carnal ","phoneNumber":"+888888"}]}'
    ),
    -- new user:
    (
        '8bf3c07b-abb2-43c1-8659-3e29125eed7c',
        '1232222244',
        NULL,
        NULL,
        NULL,
        'ea191c5a-3cb4-40b8-a43a-0fee74ea87c7',
        DEFAULT,
        DEFAULT,
        '5cf7ef6a-34cb-4736-bf08-6184070231c7',
        '{"contacts":[{"fullName":". Mama","phoneNumber":"+444444"},{"fullName":"Carnal ","phoneNumber":"+888888"}]}'
    ),
    -- user in payment default:
    (
        '55defd85-4360-498c-a3c0-f71fb06efd2e',
        '1232222244',
        'Juan',
        'Perez',
        '<EMAIL>',
        'e0e319f1-380e-4400-bbe0-5665ec489cce',
        DEFAULT,
        DEFAULT,
        'ccca95e8-5a01-4586-ab25-f962207c77c7',
        '{"contacts":[{"fullName":". Mama","phoneNumber":"+444444"},{"fullName":"Carnal ","phoneNumber":"+888888"}]}'
    ),
    -- new user register to payment default user:
    (
        'bb92f2d9-b0be-431c-bdf0-fb37625f7609',
        '1232222244',
        NULL,
        NULL,
        NULL,
        '1e255912-bfde-4543-8fec-a66ab7b6ae6c',
        DEFAULT,
        DEFAULT,
'5cf7ef6a-34cb-4736-bf08-6184070231c7',
        '{"contacts":[{"fullName":". Mama","phoneNumber":"+444444"},{"fullName":"Carnal ","phoneNumber":"+888888"}]}'
    )
;

INSERT INTO "Propaga"."UserVerificationCode" (
        id,
        "code",
        "validAt",
        "verified",
        "createdAt",
        "updatedAt",
        "userId"
    )
    VALUES  (
        'e4c08f5b-65a5-4f01-a4c5-e92d2a30153e',
        '2345',
        NOW() + INTERVAL '1 hour',
        false,
        DEFAULT,
        DEFAULT,
        '8bf3c07b-abb2-43c1-8659-3e29125eed7c'
    ), (
        'a1aa283e-093d-40ef-a8c9-1b194d0c84b3',
        '0987',
        NOW() + INTERVAL '1 hour',
        false,
        DEFAULT,
        DEFAULT,
        'bb92f2d9-b0be-431c-bdf0-fb37625f7609'
),
(
    'ef2f3a9c-656c-4003-9247-7d3c61878006',
    '9283',
    NOW() + INTERVAL '1 hour',
    false,
    DEFAULT,
    DEFAULT,
    'bb92f2d9-b0be-431c-bdf0-fb37625f7609'
    )   
;

INSERT INTO "Propaga"."WholesalerInformation" (
        id,
        "wholesalerId",
        "statusId",
        "externalId",
        data,
        "userId",
        "createdAt",
        "updatedAt"
    )
VALUES 
    -- current user:
    (
        '178d92e7-1372-4efd-b012-6a6d037c228a',
        '3128f984-f252-4f9e-90fa-4c7417f2a17a',
        '1393b08f-2483-4d53-b1de-2bbb93c78af0',
        '44444',
        '{}',
        '6e2b1a52-b039-4b5d-914f-7066c9daff2a',
        DEFAULT,
        DEFAULT
    ),
    -- new user:
    (
        '4705ca30-56da-455f-8afb-7fe9dbbbab24',
        '3128f984-f252-4f9e-90fa-4c7417f2a17a',
        '1393b08f-2483-4d53-b1de-2bbb93c78af0',
        '44444',
        '{}',
        '8bf3c07b-abb2-43c1-8659-3e29125eed7c',
        DEFAULT,
        DEFAULT
    ),
    -- user in payment default:
    (
        '4ea34263-8e1f-4f1c-8b56-9ce27c2720a0',
        '3128f984-f252-4f9e-90fa-4c7417f2a17a',
        '1393b08f-2483-4d53-b1de-2bbb93c78af0',
        '44444',
        '{}',
        '55defd85-4360-498c-a3c0-f71fb06efd2e',
        DEFAULT,
        DEFAULT
    ),
    -- new user register to payment default user:
    (
        '36134bbc-f2da-47cf-944b-baafc44a7380',
        '3128f984-f252-4f9e-90fa-4c7417f2a17a',
        '1393b08f-2483-4d53-b1de-2bbb93c78af0',
        '44444',
        '{}',
        'bb92f2d9-b0be-431c-bdf0-fb37625f7609',
        DEFAULT,
        DEFAULT
    )
;