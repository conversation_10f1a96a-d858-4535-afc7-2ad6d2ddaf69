{"name": "monoa<PERSON>", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "DEBUG=\"ioredis:*\" NODE_ENV=local pm2 start dist/main.js -i 2 -f --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint-fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "NODE_ENV=testing jest --maxWorkers=4", "test:watch": "jest --watch --maxWorkers=4", "test:cov": "jest --coverage --maxWorkers=4", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "NODE_ENV=testing jest --config ./test/jest-e2e.json --forceExit --testTimeout=120000 --detectOpenHandles --silent=false", "migrate": "NODE_ENV=local ts-node node_modules/.bin/typeorm migration:run -d src/infrastructure/database/data-source.ts", "migration:create": "ts-node node_modules/.bin/typeorm migration:create src/infrastructure/database/migrations/$npm_config_name", "migration:revert": "NODE_ENV=local ts-node node_modules/.bin/typeorm migration:revert -d src/infrastructure/database/data-source.ts", "postgres:start": "NODE_ENV=testing sh ./postgres/run.sh && npm run postgres:migrate && npm run postgres:seeders", "postgres:migrate": "NODE_ENV=testing ts-node node_modules/.bin/typeorm migration:run -d src/infrastructure/database/data-source.ts", "postgres:seeders": "NODE_ENV=testing sh ./postgres/seeders.sh", "postgres:down": "NODE_ENV=testing sh ./postgres/down.sh", "redis:start": "sh ./redis/run.sh", "redis:down": "sh ./redis/down.sh", "prepare": "./node_modules/.bin/husky install", "pre-commit": "lint-staged", "test:e2e-restart": "npm run postgres:down && npm run postgres:start && npm run test:e2e"}, "dependencies": {"@clerk/clerk-sdk-node": "^5.1.6", "@faker-js/faker": "^7.6.0", "@golevelup/ts-jest": "^0.3.5", "@googlemaps/google-maps-services-js": "^3.3.29", "@launchdarkly/node-server-sdk": "^9.7.4", "@nestjs/axios": "^2.0.0", "@nestjs/bull": "^10.0.0", "@nestjs/bullmq": "^10.0.0", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^9.0.0", "@nestjs/core": "^9.0.0", "@nestjs/event-emitter": "^2.0.0", "@nestjs/jwt": "^10.0.3", "@nestjs/platform-express": "^9.0.0", "@nestjs/schedule": "^2.2.2", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^9.0.1", "@sentry/node": "^7.47.0", "airtable": "^0.12.1", "algoliasearch": "^4.22.1", "aws-sdk": "^2.1344.0", "axios": "^1.3.4", "cache-manager": "^5.2.1", "cache-manager-ioredis": "^2.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "dd-trace": "^4.1.0", "dotenv": "^16.0.3", "express-session": "^1.17.3", "facturapi": "^3.4.0", "ioredis": "^5.4.2", "ioredis-mock": "^8.9.0", "json-2-csv": "^5.5.1", "lodash-es": "^4.17.21", "luxon": "^3.3.0", "papaparse": "^5.4.1", "pg": "^8.10.0", "redis": "^4.7.0", "redis-mock": "^0.56.3", "redlock": "^5.0.0-beta.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.2.0", "ssh2-sftp-client": "^9.1.0", "twilio": "^4.10.0", "typeorm": "^0.3.12", "vcf": "^2.1.1"}, "devDependencies": {"@commitlint/cli": "^17.6.3", "@commitlint/config-conventional": "^17.6.3", "@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/cache-manager-ioredis": "^2.0.3", "@types/cron": "^2.0.1", "@types/express": "^4.17.13", "@types/express-session": "^1.17.7", "@types/jest": "29.2.4", "@types/lodash": "^4.14.195", "@types/node": "18.11.18", "@types/papaparse": "^5.3.7", "@types/redlock": "^4.0.7", "@types/ssh2": "^1.11.13", "@types/ssh2-sftp-client": "^9.0.0", "@types/supertest": "^2.0.11", "@types/vcf": "^2.0.3", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^8.0.0", "jest": "29.3.1", "lint-staged": "^13.2.2", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.1", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": ["ts-jest", {"isolatedModules": true}]}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleDirectories": ["node_modules", "src"], "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}}