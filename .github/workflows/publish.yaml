name: Deploy to ECR

on: 
  push:
    branches: [main, develop, staging]
    
jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Install NodeJS
        uses: actions/setup-node@v2
        with:
          node-version: 18

      - name: Check out code
        uses: actions/checkout@v2

      - name: Install Dependencies
        run: npm install

      - name: Code Linting
        run: npm run lint

  build:
    needs: [lint]
    name: Build Image
    runs-on: ubuntu-latest

    steps:

    - name: Check out code
      uses: actions/checkout@v2
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2
    - name: Get branch name
      id: refbranch
      shell: bash
      run: echo "::set-output name=branch::${GITHUB_REF#refs/heads/}"

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build, tag, and push image to Amazon ECR
      if: steps.refbranch.outputs.branch != 'main'
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: mono-api
        IMAGE_TAG: ${{ steps.refbranch.outputs.branch }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

    - name: Build, tag, and push image to Amazon ECR production
      if: steps.refbranch.outputs.branch == 'main'
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: mono-api
        IMAGE_TAG: production
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

    - name: ECS refresh
      if: steps.refbranch.outputs.branch != 'main'
      uses: imehedi/actions-awscli-v2@latest
      with:
        args: ecs update-service --cluster propaga-${{ steps.refbranch.outputs.branch }} --service monapi --force-new-deployment
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_DEFAULT_REGION: "us-west-2"
    
    - name: ECS wait
      if: steps.refbranch.outputs.branch != 'main'
      uses: imehedi/actions-awscli-v2@latest
      with:
        args: ecs wait services-stable --cluster propaga-${{ steps.refbranch.outputs.branch }} --service monapi
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_DEFAULT_REGION: "us-west-2"

    - name: ECS refresh production
      if: steps.refbranch.outputs.branch == 'main'
      uses: imehedi/actions-awscli-v2@latest
      with:
        args: ecs update-service --cluster propaga-production --service monapi --force-new-deployment
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_DEFAULT_REGION: "us-west-2"

    - name: ECS wait production
      if: steps.refbranch.outputs.branch == 'main'
      uses: imehedi/actions-awscli-v2@latest
      with:
        args: ecs wait services-stable --cluster propaga-production --service monapi
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_DEFAULT_REGION: "us-west-2"

    