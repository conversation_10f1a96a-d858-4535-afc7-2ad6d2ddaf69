name: E2E Testing
on:
  pull_request:
    types: [opened, synchronize]

jobs:
  E2E:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres
        env:
          POSTGRES_USER: propagatesting
          POSTGRES_PASSWORD: password
          POSTGRES_DB: propaga
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Check out repository code
        uses: actions/checkout@v3
      
      - name: 'Create env file'
        run: |
          echo "
            host=localhost
            password=password
            username=propagatesting
            jwt_secret_key=37D1DB3DC7E9D48A5DC74DDAE631C
          " > .env.testing

      - name: Install dependencies
        run: npm install

      - name: Test postgres connection
        run: psql postgres://propagatesting:password@localhost:5432/propaga -c 'SELECT 1;'

      - name: Migrate & seed
        run: |
          npm run postgres:migrate
          psql postgres://propagatesting:password@localhost:5432/propaga < ./postgres/seeders/initial.sql
          psql postgres://propagatesting:password@localhost:5432/propaga < ./postgres/seeders/merge-account.sql
          psql postgres://propagatesting:password@localhost:5432/propaga < ./postgres/seeders/transaction-by-batch.sql
          psql postgres://propagatesting:password@localhost:5432/propaga < ./postgres/seeders/onboarding.sql
          psql postgres://propagatesting:password@localhost:5432/propaga < ./postgres/seeders/transaction-wholesalers-validations.sql
          psql postgres://propagatesting:password@localhost:5432/propaga < ./postgres/seeders/phone-number-sanitization.sql
          psql postgres://propagatesting:password@localhost:5432/propaga < ./postgres/seeders/deposit.sql
          psql postgres://propagatesting:password@localhost:5432/propaga < ./postgres/seeders/blocked-users.sql
      - name: Test
        run: npm run test:e2e