import { MigrationInterface, QueryRunner, Table, TableColumn } from 'typeorm';

export class AddImagesColumnsToCollection1747088784825 implements MigrationInterface {
  buildingPhotoUrlColumn = new TableColumn({
    name: 'buildingPhotoUrl',
    type: 'varchar',
    isNullable: true,
  });

  agentSelfiPhotoUrlColumn = new TableColumn({
    name: 'agentSelfiPhotoUrl',
    type: 'varchar',
    isNullable: true,
  });

  questionaryTable = new Table({
    name: 'Questionary',
    schema: 'Propaga',
  });

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(this.questionaryTable, this.buildingPhotoUrlColumn);
    await queryRunner.addColumn(this.questionaryTable, this.agentSelfiPhotoUrlColumn);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.questionaryTable, this.buildingPhotoUrlColumn);
    await queryRunner.dropColumn(this.questionaryTable, this.agentSelfiPhotoUrlColumn);
  }
}
