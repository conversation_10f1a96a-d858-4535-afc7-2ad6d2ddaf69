import { MigrationInterface, QueryRunner, Table, TableColumn } from 'typeorm';

export class AddUserValidationRulesToWholesaler1748036979340 implements MigrationInterface {
  wholesalerTable = new Table({
    name: 'Wholesaler',
    schema: 'Propaga',
  });

  userValidationRules = new TableColumn({
    name: 'userValidationRules',
    type: 'json',
    isNullable: true,
  });

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(this.wholesalerTable, this.userValidationRules);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.wholesalerTable, this.userValidationRules);
  }
}
