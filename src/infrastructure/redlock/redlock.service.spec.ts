import { Test, TestingModule } from '@nestjs/testing';
import { RedlockService } from './redlock.service';
import { ConfigService } from '../config/config.service';
import { Environments } from '../../application/constants';

describe('RedlockService', () => {
  let service: RedlockService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RedlockService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key: string) => {
              switch (key) {
                case 'redis_host':
                  return 'localhost';
                case 'redis_password':
                  return 'test-password';
                default:
                  return null;
              }
            }),
          },
        },
      ],
    }).compile();

    service = module.get<RedlockService>(RedlockService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should skip initialization in test environment', async () => {
    process.env.NODE_ENV = Environments.TESTING;
    await service.onModuleInit();

    // In test environment, redlock should not be initialized
    const result = await service.withLock('test-key', 1000, async () => {
      return 'test-result';
    });

    expect(result).toBe('test-result');
  });

  it('should handle missing redis host gracefully', async () => {
    jest.spyOn(configService, 'get').mockImplementation(async (key: string) => {
      if (key === 'redis_host') return null;
      return 'test-value';
    });

    process.env.NODE_ENV = Environments.LOCAL;
    await service.onModuleInit();

    const result = await service.withLock('test-key', 1000, async () => {
      return 'test-result';
    });

    expect(result).toBe('test-result');
  });

  it('should return false for isLocked when redis is not available', async () => {
    const result = await service.isLocked('test-key');
    expect(result).toBe(false);
  });

  it('should return -1 for getLockTTL when redis is not available', async () => {
    const result = await service.getLockTTL('test-key');
    expect(result).toBe(-1);
  });
});
