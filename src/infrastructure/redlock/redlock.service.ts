import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import Redlock from 'redlock';
import Redis from 'ioredis';
import { ConfigService } from '../config/config.service';
import { Environments } from '../../application/constants';

@Injectable()
export class RedlockService implements OnModuleInit {
  private readonly logger = new Logger(RedlockService.name);
  private redlock: Redlock;
  private redis: Redis;

  constructor(private readonly configService: ConfigService) {}

  async onModuleInit() {
    await this.initializeRedlock();
  }

  private async initializeRedlock() {
    try {
      const redisHost = await this.configService.get('redis_host');
      const redisPassword = await this.configService.get('redis_password');
      const isLocal = process.env.NODE_ENV === Environments.LOCAL;
      const isTest = process.env.NODE_ENV === Environments.TESTING;

      if (isTest) {
        this.logger.log('Test environment detected, skipping Redlock initialization');
        return;
      }

      if (!redisHost) {
        this.logger.warn('No Redis host provided, Redlock will not be available');
        return;
      }

      // Create Redis connection for Redlock
      this.redis = new Redis({
        host: redisHost,
        port: 6379,
        password: redisPassword,
        tls: isLocal
          ? undefined
          : {
              checkServerIdentity: () => undefined,
            },
        db: 0,
      });

      // Initialize Redlock with the Redis instance
      this.redlock = new Redlock([this.redis], {
        // The expected clock drift; for more details see:
        // http://redis.io/topics/distlock
        driftFactor: 0.01, // multiplied by lock ttl to determine drift time

        // The max number of times Redlock will attempt to lock a resource
        // before erroring.
        retryCount: 3,

        // The time in ms between attempts
        retryDelay: 200, // time in ms

        // The max time in ms randomly added to retries
        // to improve performance under high contention
        retryJitter: 200, // time in ms

        // The minimum remaining time on a lock before an extension is automatically
        // attempted with the `using` API.
        automaticExtensionThreshold: 500, // time in ms
      });

      // Add event listeners for debugging
      this.redlock.on('clientError', (err) => {
        this.logger.error('Redlock client error:', err);
      });

      this.logger.log('Redlock initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Redlock:', error);
    }
  }

  /**
   * Acquire a distributed lock and execute a function
   * @param lockKey - Unique identifier for the lock
   * @param ttl - Time to live for the lock in milliseconds
   * @param fn - Function to execute while holding the lock
   * @returns Promise that resolves with the function result
   */
  async withLock<T>(lockKey: string, ttl: number, fn: () => Promise<T>): Promise<T | null> {
    if (!this.redlock) {
      this.logger.warn(`Redlock not available, executing function without lock: ${lockKey}`);
      return await fn();
    }

    try {
      this.logger.log(`Attempting to acquire lock: ${lockKey} (TTL: ${ttl}ms)`);

      // Use redlock.using which handles lock extension and release automatically
      return await this.redlock.using([lockKey], ttl, async () => {
        this.logger.log(`Lock acquired successfully: ${lockKey}`);

        try {
          const result = await fn();
          this.logger.log(`Function executed successfully with lock: ${lockKey}`);
          return result;
        } catch (error) {
          this.logger.error(`Error executing function with lock ${lockKey}:`, error);
          throw error;
        }
      });
    } catch (error) {
      if (error.name === 'LockError') {
        this.logger.warn(`Failed to acquire lock (already held by another process): ${lockKey}`);
        return null; // Lock was not acquired, another instance is running
      }

      this.logger.error(`Error while executing with lock ${lockKey}:`, error);
      throw error;
    }
  }

  /**
   * Check if a lock exists without trying to acquire it
   * @param lockKey - Unique identifier for the lock
   * @returns Promise that resolves to true if lock exists
   */
  async isLocked(lockKey: string): Promise<boolean> {
    if (!this.redis) {
      return false;
    }

    try {
      const result = await this.redis.get(lockKey);
      return result !== null;
    } catch (error) {
      this.logger.error(`Error checking lock status for ${lockKey}:`, error);
      return false;
    }
  }

  /**
   * Get the remaining TTL of a lock
   * @param lockKey - Unique identifier for the lock
   * @returns Promise that resolves to TTL in milliseconds, or -1 if lock doesn't exist
   */
  async getLockTTL(lockKey: string): Promise<number> {
    if (!this.redis) {
      return -1;
    }

    try {
      const ttl = await this.redis.pttl(lockKey);
      return ttl;
    } catch (error) {
      this.logger.error(`Error getting lock TTL for ${lockKey}:`, error);
      return -1;
    }
  }
}
