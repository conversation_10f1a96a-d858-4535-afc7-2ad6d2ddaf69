import { Injectable, Logger } from '@nestjs/common';
import { WholesalerStrategy } from '../../interfaces/wholesaler-strategy.interface';
import { CornerStore } from 'src/infrastructure/database/models/corner-store.entity';
import { RevenueShare } from 'src/infrastructure/database/models/revenue-share.entity';
import { Transaction } from 'src/infrastructure/database/models/transaction.entity';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { SendCreditLimitUpdate } from '../../interfaces/send-credit-limit-update.interface';
import { CalculateRevenueShareAmounts } from '../../interfaces/calculate-revenue-share-amounts.interface';
import { TransactionStatus, WholesalerNames } from 'src/application/constants';
import { DateOperations } from 'src/utils/date-operations';
import { TransactionRepository } from 'src/infrastructure/database/repositories/transaction.repository';
import { RabbitService } from '../rabbit/rabbit.service';

@Injectable()
export class AbaService implements WholesalerStrategy {
  private logger = new Logger(AbaService.name);

  constructor(
    private readonly transactionRepository: TransactionRepository,
    private readonly rabbitService: RabbitService,
  ) {}

  calculateRevenueShareAmounts(): Promise<CalculateRevenueShareAmounts> {
    return null;
  }

  async sendUserActivationConfirmation(user: Users): Promise<boolean> {
    this.logger.log(`Confirmation sent to Aba for user ${user.id}`);
    return false;
  }

  async sendCreditLimitUpdate(creditLimitUpdate: SendCreditLimitUpdate): Promise<boolean> {
    this.logger.log(`Credit limit update sent to Aba for user ${creditLimitUpdate.user.id}`);
    return false;
  }

  async sendPayment(): Promise<boolean> {
    return true;
  }

  async getTransactionsToTransfer(): Promise<
    { transactions: Transaction[]; startDate: Date; cutoffDate: Date }[]
  > {
    const { startDate, cutoffDate } = DateOperations.getTransferCutoffFilters();

    const transactions = await this.transactionRepository.findDeliveredTransactionsByDate(
      startDate,
      cutoffDate,
      WholesalerNames.ABA,
    );

    return [{ transactions, startDate, cutoffDate }];
  }

  async sendPaidTransactionUpdate(transactionId: string): Promise<boolean> {
    this.logger.log(`Status update to paid sent to Aba for transaction ${transactionId}`);
    return false;
  }

  async sendTransactionConfirmation(transaction: Transaction): Promise<boolean> {
    this.logger.log(`Sent confirmed transaction with id ${transaction.id}`);
    return true;
  }

  async sendUserRegistrationConfirmation({
    userId,
    confirmationId,
    status,
  }: {
    userId: string;
    confirmationId: string;
    status: string;
  }): Promise<boolean> {
    this.logger.log(
      `Sent to Aba user registration confirmation with id ${userId} and confirmation id ${confirmationId} with status ${status}`,
    );
    return false;
  }

  getRevenueShareReportContent(revenueShareRecords: RevenueShare[]): Promise<object[]> {
    throw new Error('Method not implemented for Aba. Records: ' + revenueShareRecords.length);
  }

  async isAllowedToCreateTransaction(cornerStore: CornerStore): Promise<boolean> {
    const [user] = cornerStore.users;

    const userIsRegisteredWithRabbit = user.wholesalerInformations.some(
      ({ wholesaler: { name } }) => name === WholesalerNames.RABBIT,
    );

    if (userIsRegisteredWithRabbit) {
      return this.rabbitService.isAllowedToCreateTransaction(cornerStore);
    }

    return true;
  }

  async getTransactionsPendingOfNotification(): Promise<Transaction[]> {
    return this.transactionRepository.findTransactionsByStatus(
      WholesalerNames.ABA,
      TransactionStatus.ON_HOLD,
    );
  }
}
