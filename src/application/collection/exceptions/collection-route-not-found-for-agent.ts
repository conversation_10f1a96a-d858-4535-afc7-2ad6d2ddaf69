import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorCodes } from '../../../infrastructure/constants';

export class CollectionRouteNotFoundForAgentException extends HttpException {
  constructor(agentId: string) {
    super(
      {
        errorCode: ErrorCodes.COLLECTION_ROUTE_NOT_FOUND_FOR_AGENT.code,
        errorMessage: `${ErrorCodes.COLLECTION_ROUTE_NOT_FOUND_FOR_AGENT.message} (${agentId})`,
      },
      HttpStatus.NOT_FOUND,
    );
  }
}
