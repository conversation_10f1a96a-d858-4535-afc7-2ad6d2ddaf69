import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorCodes } from '../../../infrastructure/constants';

export class CollectionRouteUserNotFoundException extends HttpException {
  constructor() {
    super(
      {
        errorCode: ErrorCodes.COLLECTION_ROUTE_USER_NOT_FOUND.code,
        errorMessage: ErrorCodes.COLLECTION_ROUTE_USER_NOT_FOUND.message,
      },
      HttpStatus.NOT_FOUND,
    );
  }
}
