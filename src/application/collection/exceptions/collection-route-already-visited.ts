import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorCodes } from '../../../infrastructure/constants';

export class CollectionRouteUserAlreadyVisitedException extends HttpException {
  constructor() {
    super(
      {
        errorCode: ErrorCodes.COLLECTION_ROUTE_USER_ALREADY_VISITED.code,
        errorMessage: ErrorCodes.COLLECTION_ROUTE_USER_ALREADY_VISITED.message,
      },
      HttpStatus.BAD_REQUEST,
    );
  }
}
