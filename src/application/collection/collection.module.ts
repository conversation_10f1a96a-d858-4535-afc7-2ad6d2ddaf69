import { Module } from '@nestjs/common';
import { DatabaseModule } from 'src/infrastructure/database/database.module';
import { ClerkModule } from 'src/infrastructure/external-services/clerk/clerk.module';
import { ConfigModule } from 'src/infrastructure/config/config.module';
import { CreateCollectionRouteService } from './use-cases/create-collection-route/create-collection-route.service';
import { CollectionController } from './collection.controller';
import { CreateVisitEntryService } from './use-cases/create-visit-entry/create-visit-entry.service';
import { SyncVisitEntriesService } from './use-cases/sync-visit-entries/sync-visit-entries.service';
import { GetCollectionRouteByAgentService } from './use-cases/get-collection-route-by-agent/get-collection-route-by-agent.service';
import { UploadImageService } from './use-cases/upload-image/upload-image.service';
import { FileManagerModule } from 'src/infrastructure/file-manager/file-manager.module';
@Module({
  imports: [DatabaseModule, ClerkModule, ConfigModule, FileManagerModule],
  providers: [
    CreateCollectionRouteService,
    CreateVisitEntryService,
    SyncVisitEntriesService,
    GetCollectionRouteByAgentService,
    UploadImageService,
  ],
  exports: [CreateCollectionRouteService],
  controllers: [CollectionController],
})
export class CollectionModule {}
