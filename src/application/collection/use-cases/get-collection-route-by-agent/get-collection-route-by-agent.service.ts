import { Injectable } from '@nestjs/common';
import { CollectionRouteRepository } from 'src/infrastructure/database/repositories/collection-route.repository';
import { GetCollectionRouteByAgentMapper } from '../../mappers/get-collection-route-by-agent.mapper';
import { CollectionRouteNotFoundForAgentException } from '../../exceptions/collection-route-not-found-for-agent';
import { UserStatus } from 'src/application/constants';
import { CollectionRouteUser } from 'src/infrastructure/database/models/collection-route-user.entity';
import { AgentNotFoundException } from '../../exceptions/agent-not-found';

const DEFAULT_PAGE = 1;
const DEFAULT_LIMIT = 10;

@Injectable()
export class GetCollectionRouteByAgentService {
  constructor(private readonly collectionRouteRepository: CollectionRouteRepository) {}

  private getTotalRecords(collectionRouteUsers: CollectionRouteUser[]) {
    return collectionRouteUsers.filter(
      (routeUser) =>
        routeUser.user.status.name === UserStatus.IN_PAYMENT_DEFAULT ||
        routeUser.user.status.name === UserStatus.IN_PAYMENT_PLAN,
    ).length;
  }

  async handler(agentExternalId: string, page = DEFAULT_PAGE, limit = DEFAULT_LIMIT) {
    try {
      const agent = await this.collectionRouteRepository.getAgentByExternalId(agentExternalId);

      if (!agent) {
        throw new AgentNotFoundException();
      }

      const collectionRoute =
        await this.collectionRouteRepository.findLatestCollectionRouteByAgentId(agent.id);

      if (!collectionRoute) {
        throw new CollectionRouteNotFoundForAgentException(agent.id);
      }

      const collectionRouteUsers =
        await this.collectionRouteRepository.findCollectionRouteUsersByCollectionRouteId(
          collectionRoute.id,
          page,
          limit,
        );

      const response = GetCollectionRouteByAgentMapper.toResponse(
        collectionRoute,
        collectionRouteUsers,
      );

      const totalRecords = this.getTotalRecords(collectionRoute.collectionRouteUsers);

      return {
        ...response,
        pagination: {
          total: totalRecords,
          page,
          limit,
          totalPages: Math.ceil(totalRecords / limit),
        },
      };
    } catch (error) {
      console.error('Error in GetCollectionRouteByAgentService:', error);
      throw error;
    }
  }
}
