import { Test, TestingModule } from '@nestjs/testing';
import { GetCollectionRouteByAgentService } from './get-collection-route-by-agent.service';

describe('GetCollectionRouteByAgentService', () => {
  let service: GetCollectionRouteByAgentService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [GetCollectionRouteByAgentService],
    }).compile();

    service = module.get<GetCollectionRouteByAgentService>(GetCollectionRouteByAgentService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
