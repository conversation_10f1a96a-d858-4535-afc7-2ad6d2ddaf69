import { Injectable, Logger } from '@nestjs/common';
import { CollectionRouteRepository } from 'src/infrastructure/database/repositories/collection-route.repository';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { AgentNotFoundException } from '../../exceptions/agent-not-found';

@Injectable()
export class CreateCollectionRouteService {
  logger = new Logger(CreateCollectionRouteService.name);

  constructor(
    private readonly collectionRouteRepository: CollectionRouteRepository,
    private readonly userRepository: UserRepository,
  ) {}

  async handler({ agentId, userIds }: { agentId: string; userIds: string[] }) {
    const agent = await this.collectionRouteRepository.getAgentById(agentId);

    if (!agent) {
      throw new AgentNotFoundException();
    }

    const collectionRoute = await this.collectionRouteRepository.createCollectionRoute(agentId);

    for (const userId of userIds) {
      const user = await this.userRepository.findUserById(userId);

      if (!user) {
        this.logger.error(
          `User ${userId} not found to add to collection route ${collectionRoute.id}`,
        );
        continue;
      }

      await this.collectionRouteRepository.createCollectionRouteUser({
        userId,
        collectionRouteId: collectionRoute.id,
      });
    }

    return {
      collectionRouteId: collectionRoute.id,
    };
  }
}
