import { Injectable } from '@nestjs/common';
import { VisitEntry } from '../../interfaces/visit-entry';
import { CreateVisitEntryService } from '../create-visit-entry/create-visit-entry.service';
import { ValidationResults } from 'src/application/constants';

@Injectable()
export class SyncVisitEntriesService {
  constructor(private readonly createVisitEntryService: CreateVisitEntryService) {}

  async handler(visitEntries: VisitEntry[]) {
    const results = await Promise.allSettled(
      visitEntries.map((visitEntry) => this.createVisitEntryService.handler(visitEntry)),
    );

    const successfulResults = results.filter(
      (result) => result.status === ValidationResults.FULFILLED,
    );
    const failedResults = results.filter((result) => result.status === ValidationResults.REJECTED);

    return {
      received: visitEntries.length,
      processed: successfulResults.length,
      failed: failedResults.length,
      results,
    };
  }
}
