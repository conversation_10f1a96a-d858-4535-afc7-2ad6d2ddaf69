import { SyncVisitEntriesService } from './sync-visit-entries.service';
import { CreateVisitEntryService } from '../create-visit-entry/create-visit-entry.service';
import { ValidationResults } from 'src/application/constants';

describe('SyncVisitEntriesService', () => {
  let service: SyncVisitEntriesService;
  let createVisitEntryService: CreateVisitEntryService;

  beforeEach(() => {
    createVisitEntryService = {
      handler: jest.fn(),
    } as any;

    service = new SyncVisitEntriesService(createVisitEntryService);
  });

  it('should process all visit entries and return summary', async () => {
    const visitEntries = [
      {
        collectionRouteId: '1',
        collectionRouteUserId: 'u1',
        visitedDate: new Date('2024-06-01'),
        questionary: {},
      },
      {
        collectionRouteId: '2',
        collectionRouteUserId: 'u2',
        visitedDate: new Date('2024-06-01'),
        questionary: {},
      },
    ];

    (createVisitEntryService.handler as jest.Mock)
      .mockResolvedValueOnce('ok1')
      .mockRejectedValueOnce(new Error('fail'));

    const result = await service.handler(visitEntries);

    expect(result.received).toBe(2);
    expect(result.processed).toBe(1);
    expect(result.failed).toBe(1);

    expect(result.results[0].status).toBe(ValidationResults.FULFILLED);
    expect(result.results[1].status).toBe(ValidationResults.REJECTED);
    expect(result.results[0]).toHaveProperty('value', 'ok1');
    if (result.results[1].status === ValidationResults.REJECTED) {
      expect(result.results[1]).toHaveProperty('reason');
      expect(result.results[1].reason).toBeInstanceOf(Error);
      expect(result.results[1].reason.message).toBe('fail');
    }
  });
});
