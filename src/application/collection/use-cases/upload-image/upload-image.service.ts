import { Injectable } from '@nestjs/common';
import { FileManagerService } from 'src/infrastructure/file-manager/file-manager.service';
import { CollectionRouteRepository } from 'src/infrastructure/database/repositories/collection-route.repository';
import { randomUUID } from 'node:crypto';
import { BucketNames } from 'src/infrastructure/constants';
import { QuestionaryNotFoundException } from '../../exceptions/questionary-not-found';

const CONTENT_TYPE = 'image/jpeg';

@Injectable()
export class UploadImageService {
  constructor(
    private readonly fileManagerService: FileManagerService,
    private readonly collectionRouteRepository: CollectionRouteRepository,
  ) {}

  async uploadImage(questionaryId: string, buffer: Buffer) {
    try {
      const buildingPhotoFileName = [
        BucketNames.COLLECTION_ROUTE_IMAGES,
        questionaryId,
        randomUUID() + '.jpg',
      ].join('/');

      const { Key } = await this.fileManagerService.uploadFile({
        buffer: buffer,
        fileName: buildingPhotoFileName,
        contentType: CONTENT_TYPE,
        bucketName: BucketNames.COLLECTION_ROUTE_IMAGES,
      });

      return Key;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw new Error('Failed to upload image');
    }
  }

  sanitizeBase64(base64Content: string) {
    return Buffer.from(base64Content.replace(/^data:image\/\w+;base64,/, ''), 'base64');
  }

  async handler({
    buildingPhotoBase64Content,
    agentSelfiPhotoBase64Content,
    questionaryId,
  }: {
    buildingPhotoBase64Content: string;
    agentSelfiPhotoBase64Content: string;
    questionaryId: string;
  }) {
    const questionary = await this.collectionRouteRepository.findQuestionaryById(questionaryId);

    if (!questionary) {
      throw new QuestionaryNotFoundException();
    }

    const buildingPhotoBuffer = this.sanitizeBase64(buildingPhotoBase64Content);
    const agentSelfiPhotoBuffer = this.sanitizeBase64(agentSelfiPhotoBase64Content);

    const [buildingPhotoUrl, agentSelfiPhotoUrl] = await Promise.all([
      this.uploadImage(questionaryId, buildingPhotoBuffer),
      this.uploadImage(questionaryId, agentSelfiPhotoBuffer),
    ]);

    await this.collectionRouteRepository.updateCollectionRouteUser({
      questionaryId,
      buildingPhotoUrl,
      agentSelfiPhotoUrl,
    });

    return {
      buildingPhotoUrl,
      agentSelfiPhotoUrl,
    };
  }
}
