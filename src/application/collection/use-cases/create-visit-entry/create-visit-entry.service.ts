import { Injectable } from '@nestjs/common';
import { CollectionRouteRepository } from 'src/infrastructure/database/repositories/collection-route.repository';
import { CollectionRouteUserNotFoundException } from '../../exceptions/collection-route-not-found';
import { VisitEntry } from '../../interfaces/visit-entry';

@Injectable()
export class CreateVisitEntryService {
  constructor(private readonly collectionRouteRepository: CollectionRouteRepository) {}

  async handler({
    collectionRouteId,
    collectionRouteUserId,
    visitedDate,
    questionary,
  }: VisitEntry) {
    const collectionRouteUser = await this.collectionRouteRepository.getCollectionRouteUserById({
      collectionRouteId,
      collectionRouteUserId,
    });

    if (!collectionRouteUser) {
      throw new CollectionRouteUserNotFoundException();
    }

    await this.collectionRouteRepository.registerVisitCollectionRouteUser({
      collectionRouteUserId,
      visitedDate,
    });

    const questionaryResponse = await this.collectionRouteRepository.createQuestionary({
      userId: collectionRouteUser.user.id,
      collectionRouteUserId,
      questionary,
      createdAt: visitedDate,
    });

    return {
      message: 'Visit entry created successfully',
      questionaryId: questionaryResponse.id,
    };
  }
}
