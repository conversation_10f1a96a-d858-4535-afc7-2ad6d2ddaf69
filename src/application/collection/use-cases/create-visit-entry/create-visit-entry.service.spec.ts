import { CreateVisitEntryService } from './create-visit-entry.service';
import { CollectionRouteUserNotFoundException } from '../../exceptions/collection-route-not-found';
import { CollectionRouteUserAlreadyVisitedException } from '../../exceptions/collection-route-already-visited';
import { CollectionRouteRepository } from 'src/infrastructure/database/repositories/collection-route.repository';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { UserVerificationTypesEnum } from 'src/application/constants';

describe('CreateVisitEntryService', () => {
  let service: CreateVisitEntryService;
  let collectionRouteRepository: jest.Mocked<CollectionRouteRepository>;

  beforeEach(() => {
    collectionRouteRepository = {
      getCollectionRouteUserById: jest.fn(),
      registerVisitCollectionRouteUser: jest.fn(),
      createQuestionary: jest.fn(),
    } as any;

    service = new CreateVisitEntryService(collectionRouteRepository);
  });

  const baseEntry = {
    collectionRouteId: 'route1',
    collectionRouteUserId: 'user1',
    visitedDate: new Date(),
    questionary: { foo: 'bar' },
  };

  const mockUser: Users = {
    id: 'user1',
    phoneNumber: '1234567890',
    firstName: 'Test',
    lastNames: 'User',
    email: '<EMAIL>',
    dataVerification: {},
    isBlockedToSendMessages: false,
    verificationType: UserVerificationTypesEnum.COMPLETE,
    createdAt: new Date(),
    updatedAt: new Date(),
    registerDate: new Date(),
    birthdate: new Date(),
    additionalInformation: {},
    newMergedUserId: null,
    cornerStore: null,
    wholesalerInformations: [],
    status: {
      id: 'status1',
      name: 'ACTIVE',
      createdAt: new Date(),
      users: [],
      userHistories: [],
    } as any,
    userVerificationCodes: [] as any,
    userAuthenticationCode: [] as any,
    userHistories: [] as any,
    notificationHistory: [] as any,
    userValidationResults: [] as any,
    defaultPenalties: [] as any,
    paymentPlans: [] as any,
    paymentInformations: [] as any,
    phoneNumberHistory: [] as any,
    signUpStatus: {
      id: 'signup1',
      name: 'COMPLETED',
      createdAt: new Date(),
      updatedAt: new Date(),
      users: [],
      userHistories: [],
    } as any,
    userDetail: null,
    newMergedUser: null,
    collectionRoutes: [] as any,
    questionaries: [] as any,
    getDefaultPenaltyAmountToPay: () => 0,
    isInDefaultPayment: () => false,
    isBlocked: () => false,
    isVerified: () => true,
    isSignUpValidated: () => true,
    isMerged: () => false,
  };

  const mockCollectionRouteUser = {
    id: 'cru1',
    user: mockUser,
    visitedDate: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    collectionRoute: null,
    questionaries: [],
  };

  it('should create a visit entry successfully', async () => {
    collectionRouteRepository.getCollectionRouteUserById.mockResolvedValue(mockCollectionRouteUser);
    collectionRouteRepository.registerVisitCollectionRouteUser.mockResolvedValue(undefined);
    collectionRouteRepository.createQuestionary.mockResolvedValue(undefined);

    const result = await service.handler(baseEntry);

    expect(collectionRouteRepository.getCollectionRouteUserById).toHaveBeenCalledWith({
      collectionRouteId: baseEntry.collectionRouteId,
      collectionRouteUserId: baseEntry.collectionRouteUserId,
    });
    expect(collectionRouteRepository.registerVisitCollectionRouteUser).toHaveBeenCalledWith({
      collectionRouteUserId: baseEntry.collectionRouteUserId,
      visitedDate: baseEntry.visitedDate,
    });
    expect(collectionRouteRepository.createQuestionary).toHaveBeenCalledWith({
      userId: 'user1',
      collectionRouteUserId: baseEntry.collectionRouteUserId,
      questionary: baseEntry.questionary,
      createdAt: baseEntry.visitedDate,
    });
    expect(result).toEqual({ message: 'Visit entry created successfully' });
  });

  it('should throw CollectionRouteUserNotFoundException if user not found', async () => {
    collectionRouteRepository.getCollectionRouteUserById.mockResolvedValue(null);

    await expect(service.handler(baseEntry)).rejects.toBeInstanceOf(
      CollectionRouteUserNotFoundException,
    );
  });

  it('should throw CollectionRouteUserAlreadyVisitedException if already visited', async () => {
    collectionRouteRepository.getCollectionRouteUserById.mockResolvedValue({
      ...mockCollectionRouteUser,
      visitedDate: new Date(),
    });

    await expect(service.handler(baseEntry)).rejects.toBeInstanceOf(
      CollectionRouteUserAlreadyVisitedException,
    );
  });
});
