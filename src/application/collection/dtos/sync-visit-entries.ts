import { Type } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsNotEmpty,
  IsObject,
  IsString,
  ValidateNested,
} from 'class-validator';

export class SyncVisitEntry {
  @IsString()
  @IsNotEmpty()
  collectionRouteId: string;

  @IsString()
  @IsNotEmpty()
  collectionRouteUserId: string;

  @IsDateString()
  @IsNotEmpty()
  visitedDate: Date;

  @IsObject()
  @IsNotEmpty()
  questionary: object;
}

export class SyncVisitEntriesBodyDTO {
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => SyncVisitEntry)
  visitEntries: SyncVisitEntry[];
}
