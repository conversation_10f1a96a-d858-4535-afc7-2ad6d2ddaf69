import { Numbers } from 'src/application/constants';
import { CollectionRouteUser } from 'src/infrastructure/database/models/collection-route-user.entity';
import { CollectionRoute } from 'src/infrastructure/database/models/collection-route.entity';

export class GetCollectionRouteByAgentMapper {
  static toResponse(collectionRoute: CollectionRoute, collectionRouteUsers: CollectionRouteUser[]) {
    const mappedRoutes = collectionRouteUsers.map((routeUser) => {
      const totalAmountTransactions = routeUser.user.cornerStore
        .getTransactionsReadyToPay()
        .reduce((acc, transaction) => acc + transaction.totalAmountWithInterests, Numbers.ZERO);
      const totalAmountPaymentDefault = routeUser.user.getDefaultPenaltyAmountToPay();

      return {
        collectionRouteUserId: routeUser.id,
        visitedDate: routeUser.visitedDate,
        totalAmountTransactions,
        totalAmountPaymentDefault,
        totalAmountToPay: totalAmountTransactions + totalAmountPaymentDefault,
        user: {
          id: routeUser.user.id,
          firstName: routeUser.user.firstName,
          lastName: routeUser.user.lastNames,
          email: routeUser.user.email,
          phone: routeUser.user.phoneNumber,
          address: routeUser.user.cornerStore.address,
          latitude: routeUser.user.cornerStore.location['latitude'] || Numbers.ZERO,
          longitude: routeUser.user.cornerStore.location['longitude'] || Numbers.ZERO,
          wholesaler: routeUser.user.wholesalerInformations
            .map(({ wholesaler }) => wholesaler.name)
            .join(', '),
        },
        transactions: routeUser.user.cornerStore.transactions.map((transaction) => ({
          id: transaction.id,
          totalAmountWithInterests: transaction.totalAmountWithInterests,
          movementDate: transaction.movementDate,
          deliveryDate: transaction.actualDeliveryDate,
          paymentDate: transaction.paymentDate,
          status: transaction.status.name,
          wholesaler: transaction.wholesaler.name,
          products: transaction.products.map((product) => ({
            name: product.name,
            quantity: product.quantity,
          })),
        })),
      };
    });

    return {
      collectionRouteId: collectionRoute.id,
      routeUsers: mappedRoutes,
    };
  }
}
