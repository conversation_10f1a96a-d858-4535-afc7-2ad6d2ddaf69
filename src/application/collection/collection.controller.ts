import { Controller, Post, Body, Param, UseGuards, Get, Query, Req } from '@nestjs/common';
import { CreateVisitEntryService } from './use-cases/create-visit-entry/create-visit-entry.service';
import { ClerkGuard } from 'src/authentication/clerk.guard';
import { CreateVisitEntryBodyDTO, CreateVisitEntryParamsDTO } from './dtos/create-visit-entry';
import { SyncVisitEntriesService } from './use-cases/sync-visit-entries/sync-visit-entries.service';
import { UploadImageService } from './use-cases/upload-image/upload-image.service';
import { SyncVisitEntriesBodyDTO } from './dtos/sync-visit-entries';
import { GetCollectionRouteByAgentService } from './use-cases/get-collection-route-by-agent/get-collection-route-by-agent.service';
import { Numbers } from '../constants';
import { Request } from 'express';
import { UploadImageDTO } from './dtos/upload-image';

@Controller('collection')
export class CollectionController {
  constructor(
    private readonly createVisitEntryService: CreateVisitEntryService,
    private readonly syncVisitEntriesService: SyncVisitEntriesService,
    private readonly getCollectionRouteByAgentService: GetCollectionRouteByAgentService,
    private readonly uploadImageService: UploadImageService,
  ) {}

  @UseGuards(ClerkGuard)
  @Post('/route/:collectionRouteId/user/:collectionRouteUserId')
  async createVisitEntry(
    @Param()
    { collectionRouteId, collectionRouteUserId }: CreateVisitEntryParamsDTO,
    @Body() { visitedDate, questionary }: CreateVisitEntryBodyDTO,
  ) {
    return this.createVisitEntryService.handler({
      collectionRouteId,
      collectionRouteUserId,
      visitedDate,
      questionary,
    });
  }

  @UseGuards(ClerkGuard)
  @Post('/image/:questionaryId')
  async uploadImage(
    @Body()
    { buildingPhotoBase64Content, agentSelfiPhotoBase64Content }: UploadImageDTO,
    @Param()
    { questionaryId }: { questionaryId: string },
  ) {
    return this.uploadImageService.handler({
      buildingPhotoBase64Content,
      agentSelfiPhotoBase64Content,
      questionaryId,
    });
  }

  @UseGuards(ClerkGuard)
  @Post('/route/sync')
  async syncVisitEntries(@Body() { visitEntries }: SyncVisitEntriesBodyDTO) {
    return this.syncVisitEntriesService.handler(visitEntries);
  }

  @UseGuards(ClerkGuard)
  @Get('/route/agent/sync')
  async getCollectionRouteByAgent(@Req() req: Request, @Query('page') page?: number) {
    const agentExternalId = req['agentExternalId'];
    return this.getCollectionRouteByAgentService.handler(
      agentExternalId,
      page ? parseInt(page.toString(), Numbers.TEN) : Numbers.ONE,
    );
  }
}
