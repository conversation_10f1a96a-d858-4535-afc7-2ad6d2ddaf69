import {
  Controller,
  Req,
  Get,
  UseInterceptors,
  Post,
  Body,
  UseGuards,
  Query,
} from '@nestjs/common';
import { Request } from 'express';
import { GetCustomizerOptionsService } from './use-cases/get-customizer-options/get-customizer-options.service';
import { SentryInterceptor } from 'src/middlewares/error.interceptor';
import { UpdateInvoicesDataService } from './use-cases/update-invoices-data/update-invoices-data.service';
import { UpdateInvoicesDataDTO } from './dtos/update-invoices-data';
import { SetupNewWholesalerService } from './use-cases/setup-new-wholesaler/setup-new-wholesaler.service';
import { InternalTokenGuard } from 'src/authentication/internal-token.guard';
import { CreateKrakenUsersService } from './use-cases/create-kraken-users/create-kraken-users.service';
import { CreateKrakenUsersDTO } from './dtos/create-kraken-users';
import { FindWholesalerTransfersService } from './use-cases/find-wholesaler-transfers/find-wholesaler-transfers.service';

@UseInterceptors(SentryInterceptor)
@Controller('wholesaler')
export class WholesalerController {
  constructor(
    private readonly getCustomizerOptionsService: GetCustomizerOptionsService,
    private readonly updateInvoicesData: UpdateInvoicesDataService,
    private readonly setupNewWholesalerService: SetupNewWholesalerService,
    private readonly createKrakenUsersService: CreateKrakenUsersService,
    private readonly findWholesalerTransfersService: FindWholesalerTransfersService,
  ) {}

  @Get('/whoami')
  whoAmI() {
    return {
      task: process.env.ECS_TASK_ID || process.env.HOSTNAME,
    };
  }

  @Get('/customizer-options')
  getCustomizerOptions(@Req() req: Request) {
    const wholesalerId = req['wholesalerId'];
    return this.getCustomizerOptionsService.handler(wholesalerId);
  }

  @Post('/transfer/invoices')
  saveInvoicesData(
    @Body()
    { wholesalerName, fileName, invoices }: UpdateInvoicesDataDTO,
  ) {
    return this.updateInvoicesData.handler({
      wholesalerName,
      fileName,
      invoices,
    });
  }

  @UseGuards(InternalTokenGuard)
  @Post('/setup')
  setupNewWholesaler(
    @Body() { wholesalerName, metadata }: { wholesalerName: string; metadata: any },
  ) {
    return this.setupNewWholesalerService.handler({ wholesalerName, metadata });
  }

  @UseGuards(InternalTokenGuard)
  @Post('/users')
  createKrakenUsers(@Body() { wholesalerName, users }: CreateKrakenUsersDTO) {
    return this.createKrakenUsersService.handler({ wholesalerName, users });
  }

  @Get('/transfers')
  getTransfers(
    @Req() req: Request,
    @Query('page') page = 1,
    @Query('referenceNumber') referenceNumber?: string,
    @Query('status') status?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const wholesalerId = req['wholesalerId'];
    return this.findWholesalerTransfersService.handler(wholesalerId, page, {
      referenceNumber,
      status,
      dateRange: {
        start: startDate ? new Date(startDate) : undefined,
        end: endDate ? new Date(endDate) : undefined,
      },
    });
  }
}
