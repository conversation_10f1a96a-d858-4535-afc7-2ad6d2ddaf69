import { Injectable } from '@nestjs/common';
import { UserRepository } from '../../../../infrastructure/database/repositories/user.repository';
import { UserStatus, UserVerificationTypesEnum } from 'src/application/constants';
import { UserIsVerifiedException } from '../../exceptions/user-is-verified';
import { UserInManualValidationException } from '../../exceptions/user-in-manual-validation';
import { WhatsappService } from 'src/infrastructure/external-services/whatsapp/whatsapp.service';
import { HttpStatusCode } from 'axios';
import { MessageNotSendException } from '../../exceptions/message-not-send';
import { CreateLightVerificationUseCaseDTO } from '../../dtos/create-light-verification';
import { CreateVerificationMapper } from '../../mappers/create-verification-code.mapper';
import { UserIsBlockedException } from 'src/application/transaction/exceptions/user-is-blocked';
import { OtpOperations } from 'src/utils/otp-operations';
import { WhatsappTemplates } from 'src/infrastructure/external-services/whatsapp/templates';

@Injectable()
export class CreateLightVerificationService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly whatsappService: WhatsappService,
  ) {}

  async handler({ userId, phoneNumber }: CreateLightVerificationUseCaseDTO) {
    const user = await this.userRepository.findUserById(userId);

    if (user.status.name === UserStatus.VALIDATED) {
      throw new UserIsVerifiedException();
    }

    if (user.status.name === UserStatus.MANUAL_VALIDATION) {
      throw new UserInManualValidationException();
    }

    if (user.status.name === UserStatus.MERGED) {
      throw new UserIsBlockedException();
    }

    const { code, validAt } = OtpOperations.generateOtp();
    const verification = await this.userRepository.createVerificationCode(userId, code, validAt);

    const whatsappResponse = await this.whatsappService.sendMessageByTemplate(phoneNumber, {
      template: WhatsappTemplates.OTP_VERIFICATION,
      parameters: [verification.code.toString()],
    });

    if (whatsappResponse.status !== HttpStatusCode.Created) {
      throw new MessageNotSendException();
    }

    await this.userRepository.updatePhoneNumberWithVerification({
      userId,
      phoneNumber,
      verificationType: UserVerificationTypesEnum.SIMPLE,
    });

    return CreateVerificationMapper.toResponse(verification);
  }
}
