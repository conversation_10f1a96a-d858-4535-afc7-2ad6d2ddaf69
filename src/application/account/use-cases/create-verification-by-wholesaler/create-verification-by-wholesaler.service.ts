import { Injectable, Logger } from '@nestjs/common';
import { UserRepository } from '../../../../infrastructure/database/repositories/user.repository';
import { CreateVerificationUseCaseDTO } from '../../dtos/create-verification';
import { UserIsVerifiedException } from '../../exceptions/user-is-verified';
import { CornerStoreRepository } from 'src/infrastructure/database/repositories/corner-store.repository';
import { CreateVerificationByWholesalerMapper } from '../../mappers/create-verification-by-wholesaler.mapper';
import { WholesalerRepository } from 'src/infrastructure/database/repositories/wholesaler.repository';
import { UserStatus, WholesalerInformationStatus } from 'src/application/constants';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UserInManualValidationException } from '../../exceptions/user-in-manual-validation';
import { UserIsBlockedException } from 'src/application/transaction/exceptions/user-is-blocked';
import { DateOperations } from '../../../../utils/date-operations';

@Injectable()
export class CreateVerificationByWholesalerService {
  logger = new Logger(CreateVerificationByWholesalerService.name);
  constructor(
    private readonly userRepository: UserRepository,
    private readonly cornerStoreRepository: CornerStoreRepository,
    private readonly wholesalerRepository: WholesalerRepository,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  public async handler({
    phoneNumber,
    userId,
    wholesalerName,
    latitude,
    longitude,
    firstName,
    lastNames,
    contacts,
    email,
    contactSelectionType,
    birthdate,
  }: CreateVerificationUseCaseDTO) {
    const user = await this.userRepository.findUserById(userId);
    if (
      user.status.name === UserStatus.VALIDATED ||
      user.status.name === UserStatus.VALIDATED_BY_WHOLESALER
    ) {
      throw new UserIsVerifiedException();
    }

    if (user.status.name === UserStatus.MANUAL_VALIDATION) {
      throw new UserInManualValidationException();
    }

    if (user.status.name === UserStatus.MERGED) {
      throw new UserIsBlockedException();
    }

    if (latitude && longitude && user.cornerStore && user.cornerStore.id) {
      this.logger.log(`Saving location of corner store with id ${user.cornerStore.id}`);
      await this.cornerStoreRepository.saveLocation(user.cornerStore.id, {
        latitude,
        longitude,
      });
    }

    await this.userRepository.updatePhoneNumberAndName({
      userId,
      phoneNumber,
      firstName,
      lastNames,
      contacts,
      email,
      birthdate: birthdate ? DateOperations.getCstDate(birthdate) : null,
      contactSelectionType,
    });

    await this.userRepository.createPhoneNumberHistory({
      user,
      phoneNumber,
      isConfirmed: true,
    });
    await this.userRepository.updateUserStatus(userId, UserStatus.VALIDATED_BY_WHOLESALER);
    await this.wholesalerRepository.setWholesalerInformationStatus(
      userId,
      WholesalerInformationStatus.APPROVED,
    );

    this.logger.log(`User ${userId} validated by wholesaler ${wholesalerName}`);

    this.eventEmitter.emit('account.verificated', {
      user,
      cornerStore: user.cornerStore,
      phoneNumber,
      wholesalerName,
    });

    return CreateVerificationByWholesalerMapper.toResponse(user);
  }
}
