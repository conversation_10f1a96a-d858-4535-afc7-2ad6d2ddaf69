import { Test, TestingModule } from '@nestjs/testing';
import { CreateVerificationByWholesalerService } from './create-verification-by-wholesaler.service';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { createMock } from '@golevelup/ts-jest';
import { CornerStoreRepository } from 'src/infrastructure/database/repositories/corner-store.repository';
import { WholesalerRepository } from 'src/infrastructure/database/repositories/wholesaler.repository';

describe('CreateVerificationByWholesalerService', () => {
  let service: CreateVerificationByWholesalerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateVerificationByWholesalerService,
        {
          provide: UserRepository,
          useValue: createMock<UserRepository>(),
        },
        {
          provide: CornerStoreRepository,
          useValue: createMock<CornerStoreRepository>(),
        },
        {
          provide: WholesalerRepository,
          useValue: createMock<WholesalerRepository>(),
        },
      ],
    }).compile();

    service = module.get<CreateVerificationByWholesalerService>(
      CreateVerificationByWholesalerService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
