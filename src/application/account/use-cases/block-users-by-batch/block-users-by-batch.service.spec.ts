import { Test, TestingModule } from '@nestjs/testing';
import { BlockUsersByBatchService } from './block-users-by-batch.service';

describe('BlockUsersByBatchService', () => {
  let service: BlockUsersByBatchService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [BlockUsersByBatchService],
    }).compile();

    service = module.get<BlockUsersByBatchService>(BlockUsersByBatchService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
