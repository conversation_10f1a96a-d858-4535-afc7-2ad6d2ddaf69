import { Injectable, Logger } from '@nestjs/common';
import { UserBlockingSources } from 'src/application/constants';
import { WholesalerRepository } from 'src/infrastructure/database/repositories/wholesaler.repository';
import { BlockUsersByBatchMapper } from '../../mappers/block-users-by-batch.mapper';
import { WholesalerInformation } from 'src/infrastructure/database/models/wholesaler-information.entity';

@Injectable()
export class BlockUsersByBatchService {
  logger = new Logger(BlockUsersByBatchService.name);
  constructor(private readonly wholesalerRepository: WholesalerRepository) {}

  private async manageUserBlocking(wholesalerInformation: WholesalerInformation) {
    if (wholesalerInformation.isBlocked) {
      return;
    }

    return this.wholesalerRepository.updateWholesalerInformationIsBlocked({
      wholesalerInformation: wholesalerInformation,
      isBlocked: true,
      source: UserBlockingSources.BATCH_BLOCK,
    });
  }

  public async handler(records: unknown[], wholesalerId: string) {
    const wholesalerInformations = await this.wholesalerRepository.findWholesalerInformationByBatch(
      wholesalerId,
      records.map((record) => record['user_id']),
    );

    if (!wholesalerInformations.length) {
      return { message: 'Records not found' };
    }

    for (const wholesalerInformation of wholesalerInformations) {
      await this.manageUserBlocking(wholesalerInformation);
    }

    return BlockUsersByBatchMapper.toResponse(wholesalerInformations);
  }
}
