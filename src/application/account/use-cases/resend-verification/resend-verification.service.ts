import { Injectable, Logger } from '@nestjs/common';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { UserIsVerifiedException } from '../../exceptions/user-is-verified';
import { VerificationNotExistException } from '../../exceptions/verification-not-exist';
import { VerificationNotValidException } from '../../exceptions/verification-not-valid';
import { WhatsappService } from 'src/infrastructure/external-services/whatsapp/whatsapp.service';
import { MessageNotSendException } from '../../exceptions/message-not-send';
import { HttpStatusCode } from 'axios';
import { CreateVerificationMapper } from '../../mappers/create-verification-code.mapper';
import { WhatsappTemplates } from 'src/infrastructure/external-services/whatsapp/templates';

@Injectable()
export class ResendVerificationService {
  logger = new Logger(ResendVerificationService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly whatsappService: WhatsappService,
  ) {}

  public async handler({
    phoneNumber,
    userId,
    verificationId,
    wholesalerName,
  }: {
    phoneNumber: string;
    userId: string;
    verificationId: string;
    wholesalerName: string;
  }) {
    const verification = await this.userRepository.getVerification(
      verificationId,
      userId,
      wholesalerName,
    );

    if (!verification) {
      throw new VerificationNotExistException();
    }

    if (verification.verified) {
      throw new VerificationNotValidException();
    }

    if (verification.user.isVerified()) {
      throw new UserIsVerifiedException();
    }

    const whatsappResponse = await this.whatsappService.sendMessageByTemplate(phoneNumber, {
      template: WhatsappTemplates.OTP_VERIFICATION,
      parameters: [verification.code.toString()],
    });

    if (whatsappResponse.status !== HttpStatusCode.Created) {
      throw new MessageNotSendException();
    }

    this.logger.log(`Send message to user ${userId} with messageId ${whatsappResponse.data.id}`);
    return CreateVerificationMapper.toResponse(verification);
  }
}
