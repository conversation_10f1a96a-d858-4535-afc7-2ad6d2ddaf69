import { Injectable } from '@nestjs/common';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { UserNotFoundException } from '../../exceptions/user-not-found';
import { FindUserByIdMapper } from '../../mappers/find-user-by-id.mapper';

@Injectable()
export class FindUserByIdService {
  constructor(private readonly userRepository: UserRepository) {}

  async handler({ userId, wholesalerId }: { userId: string; wholesalerId: string }) {
    const user = await this.userRepository.findUserByIdAndWholesalerId(userId, wholesalerId);

    if (!user) {
      throw new UserNotFoundException();
    }
    const [wholesalerInformation] = user.wholesalerInformations;

    if (wholesalerInformation.isBlocked) {
      throw new UserNotFoundException();
    }

    return FindUserByIdMapper.toResponse(user, wholesalerInformation);
  }
}
