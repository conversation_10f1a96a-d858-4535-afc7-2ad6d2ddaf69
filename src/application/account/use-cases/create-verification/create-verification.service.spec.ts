import { Test, TestingModule } from '@nestjs/testing';
import { CreateVerificationService } from './create-verification.service';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { createMock } from '@golevelup/ts-jest';
import { CornerStoreRepository } from 'src/infrastructure/database/repositories/corner-store.repository';
import { WhatsappService } from 'src/infrastructure/external-services/whatsapp/whatsapp.service';

describe('CreateVerificationService', () => {
  let service: CreateVerificationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateVerificationService,
        {
          provide: UserRepository,
          useValue: createMock<UserRepository>(),
        },
        {
          provide: CornerStoreRepository,
          useValue: createMock<CornerStoreRepository>(),
        },
        {
          provide: WhatsappService,
          useValue: createMock<WhatsappService>(),
        },
      ],
    }).compile();

    service = module.get<CreateVerificationService>(CreateVerificationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
