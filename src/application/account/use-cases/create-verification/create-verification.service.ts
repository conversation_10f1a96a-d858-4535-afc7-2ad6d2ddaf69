import { Injectable, Logger } from '@nestjs/common';
import { UserRepository } from '../../../../infrastructure/database/repositories/user.repository';
import { Contact, CreateVerificationUseCaseDTO } from '../../dtos/create-verification';
import { UserIsVerifiedException } from '../../exceptions/user-is-verified';
import { CreateVerificationMapper } from '../../mappers/create-verification-code.mapper';
import { CornerStoreRepository } from 'src/infrastructure/database/repositories/corner-store.repository';
import { Numbers, UserStatus } from 'src/application/constants';
import { UserInManualValidationException } from '../../exceptions/user-in-manual-validation';
import { UserNotFoundException } from '../../exceptions/user-not-found';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { NotLocationFindException } from '../../exceptions/not-location-find';
import { UserIsBlockedException } from 'src/application/transaction/exceptions/user-is-blocked';
import { OtpOperations } from 'src/utils/otp-operations';
import { DateOperations } from 'src/utils/date-operations';
import { StringOperations } from 'src/utils/string-operations';

@Injectable()
export class CreateVerificationService {
  logger = new Logger(CreateVerificationService.name);
  constructor(
    private readonly userRepository: UserRepository,
    private readonly cornerStoreRepository: CornerStoreRepository,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  private async createVerificationCode(userId: string, phoneNumber: string) {
    const { code, validAt } = OtpOperations.generateOtp(Numbers.THIRTY);
    const verification = await this.userRepository.createVerificationCode(userId, code, validAt);

    this.eventEmitter.emit('account.verification.created', {
      phoneNumber,
      verificationCode: verification.code.toString(),
    });

    return verification;
  }

  private hasLocation(location?: object, latitude?: string, longitude?: string) {
    if (location) {
      return true;
    }
    if (latitude && longitude) {
      return true;
    }

    return false;
  }

  private formatPhoneNumbers(phoneNumber: string, contacts: Contact[]) {
    const formattedPhoneNumber = StringOperations.removePhoneCountryFromPhoneNumber(phoneNumber);
    const formattedContacts = contacts.map((contact) => {
      return {
        ...contact,
        phoneNumber: StringOperations.removePhoneCountryFromPhoneNumber(contact.phoneNumber),
      };
    });

    return {
      formattedPhoneNumber,
      formattedContacts,
    };
  }

  public async handler({
    phoneNumber,
    userId,
    latitude,
    longitude,
    contacts,
    firstName,
    lastNames,
    wholesalerName,
    email,
    birthdate,
    contactSelectionType,
    metadata,
  }: CreateVerificationUseCaseDTO) {
    const user = await this.userRepository.findUserById(userId);

    if (!user) {
      throw new UserNotFoundException();
    }

    if (!this.hasLocation(user.cornerStore.location, latitude, longitude)) {
      throw new NotLocationFindException();
    }

    if (user.status.name === UserStatus.VALIDATED) {
      throw new UserIsVerifiedException();
    }

    if (user.status.name === UserStatus.MANUAL_VALIDATION) {
      throw new UserInManualValidationException();
    }

    if (user.status.name === UserStatus.MERGED) {
      throw new UserIsBlockedException();
    }

    const { formattedPhoneNumber, formattedContacts } = this.formatPhoneNumbers(
      phoneNumber,
      contacts,
    );

    await this.userRepository.updatePhoneNumberAndName({
      userId,
      phoneNumber: formattedPhoneNumber,
      firstName,
      lastNames,
      contacts: formattedContacts,
      email,
      contactSelectionType,
      birthdate: birthdate ? DateOperations.getCstDate(birthdate) : null,
    });

    const verification = await this.createVerificationCode(userId, formattedPhoneNumber);

    if (metadata || phoneNumber !== formattedPhoneNumber) {
      await this.userRepository.upsertUserDetail(userId, {
        metadata: { ...metadata, receivedPhoneNumber: phoneNumber },
      });
    }

    this.eventEmitter.emit('validator.call', {
      validatePayload: {
        userId,
        phoneNumber: formattedPhoneNumber,
        latitude: latitude ?? user.cornerStore?.location['latitude'],
        longitude: longitude ?? user.cornerStore?.location['longitude'],
        contacts: formattedContacts,
        firstName,
        lastNames,
        wholesaler: wholesalerName,
        metadata,
      },
      user,
      verificationId: verification.id,
    });

    if (latitude && longitude && user.cornerStore && user.cornerStore.id) {
      this.logger.log(`Saving location of corner store with id ${user.cornerStore.id}`);
      await this.cornerStoreRepository.saveLocation(user.cornerStore.id, {
        latitude,
        longitude,
      });

      this.eventEmitter.emit('user.save.address', {
        user,
        latitude,
        longitude,
      });
    }

    return CreateVerificationMapper.toResponse(verification);
  }
}
