import { Injectable } from '@nestjs/common';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { UserNotFoundException } from '../../exceptions/user-not-found';
import {
  EMPTY_SPACE,
  UserBlockingSources,
  UserStatus,
  WholesalerNames,
} from 'src/application/constants';
import { WholesalerRepository } from 'src/infrastructure/database/repositories/wholesaler.repository';
import { ManualUpdateCreditLimitService } from 'src/application/corner-store/use-cases/manual-update-credit-limit/manual-update-credit-limit.service';
import { UserIsMergedException } from 'src/application/transaction/exceptions/user-is-merged';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CornerStoreRepository } from 'src/infrastructure/database/repositories/corner-store.repository';
import { CornerStore } from 'src/infrastructure/database/models/corner-store.entity';
const TEN_THOUSAND = 10_000;

@Injectable()
export class MergeUsersService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly cornerStoreRepository: CornerStoreRepository,
    private readonly wholesalerRepository: WholesalerRepository,
    private readonly manualUpdateCreditLimitService: ManualUpdateCreditLimitService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  private async validatBlockUser(currentUser: Users, newUser: Users) {
    if (currentUser.wholesalerInformations.some((info) => info.isBlocked)) {
      for (const wholesalerInformation of newUser.wholesalerInformations) {
        await this.wholesalerRepository.updateWholesalerInformationIsBlocked({
          wholesalerInformation: wholesalerInformation,
          isBlocked: true,
          source: UserBlockingSources.USER_MERGING,
        });
      }
    }
  }

  private async mergeRabbitUser(currentUser: Users, rabbitUser: Users) {
    await this.wholesalerRepository.updateWholesalerInformationAssigedUser({
      wholesalerInformationId: currentUser.wholesalerInformations[0].id,
      user: rabbitUser,
    });

    await this.userRepository.updateUserStatus(rabbitUser.id, currentUser.status.name);
    await this.userRepository.updateUserStatus(currentUser.id, UserStatus.MERGED);

    await this.userRepository.replaceCornerStore(rabbitUser.id, currentUser.cornerStore.id);
    await this.userRepository.replaceCornerStore(currentUser.id, rabbitUser.cornerStore.id);
    await this.wholesalerRepository.updateWholesalerInformationIsMerged(
      rabbitUser.wholesalerInformations[0].id,
    );

    await this.validatBlockUser(currentUser, rabbitUser);

    await this.wholesalerRepository.updateWholesalerInformationMetadata(
      rabbitUser.wholesalerInformations[0].id,
      {
        ...currentUser.wholesalerInformations[0].data,
        previousUserId: currentUser.id,
        previousCornerStoreId: currentUser.cornerStore.id,
        registeredDate: new Date(),
      },
    );

    await this.wholesalerRepository.updateWholesalerInformationMetadata(
      currentUser.wholesalerInformations[0].id,
      {
        ...currentUser.wholesalerInformations[0].data,
        registeredDate: currentUser.registerDate,
      },
    );

    return {
      mergedUser: await this.userRepository.findUserById(rabbitUser.id),
      deprecatedUser: await this.userRepository.findUserById(currentUser.id),
      originalUser: currentUser,
    };
  }

  private async mergeUsers(currentUser: Users, newUser: Users) {
    const newUserWholesaler = newUser.wholesalerInformations[0].wholesaler.name;

    if (newUserWholesaler === WholesalerNames.RABBIT) {
      return this.mergeRabbitUser(currentUser, newUser);
    }

    await this.wholesalerRepository.updateWholesalerInformationAssigedUser({
      wholesalerInformationId: newUser.wholesalerInformations[0].id,
      user: currentUser,
    });

    await this.userRepository.updateUserStatus(newUser.id, UserStatus.MERGED);
    await this.validatBlockUser(currentUser, newUser);

    await this.wholesalerRepository.updateWholesalerInformationMetadata(
      currentUser.wholesalerInformations[0].id,
      {
        ...currentUser.wholesalerInformations[0].data,
        previousUserId: newUser.id,
        previousCornerStoreId: newUser.cornerStore.id,
        registeredDate: currentUser.registerDate,
      },
    );

    await this.wholesalerRepository.updateWholesalerInformationMetadata(
      newUser.wholesalerInformations[0].id,
      {
        ...currentUser.wholesalerInformations[0].data,
        registeredDate: new Date(),
      },
    );

    await this.wholesalerRepository.updateWholesalerInformationIsMerged(
      currentUser.wholesalerInformations[0].id,
    );

    return {
      mergedUser: await this.userRepository.findUserById(currentUser.id),
      deprecatedUser: await this.userRepository.findUserById(newUser.id),
      originalUser: currentUser,
    };
  }

  private mergeContacts(mergedUser: Users, deprecatedUser: Users) {
    return {
      contacts: [
        ...mergedUser.dataVerification['contacts'],
        ...deprecatedUser.dataVerification['contacts'],
      ],
    };
  }

  private calculateNewCreditLimit({
    mergedCornerStore,
    deprecatedCornerStore,
    currentUser,
  }: {
    mergedCornerStore: CornerStore;
    deprecatedCornerStore: CornerStore;
    currentUser: Users;
  }) {
    const currentUserWholesaler = currentUser.wholesalerInformations[0].wholesaler.name;
    const transactionsPaid = mergedCornerStore.getPaidTransactionsInTime(currentUser);

    if (currentUserWholesaler !== WholesalerNames.RABBIT && transactionsPaid.length === 0) {
      return currentUser.cornerStore.creditLimit;
    }

    if (
      mergedCornerStore.creditLimit > TEN_THOUSAND &&
      deprecatedCornerStore.creditLimit > TEN_THOUSAND
    ) {
      return Math.max(mergedCornerStore.creditLimit, deprecatedCornerStore.creditLimit);
    }

    if (mergedCornerStore.creditLimit > TEN_THOUSAND) {
      return mergedCornerStore.creditLimit;
    }

    if (deprecatedCornerStore.creditLimit > TEN_THOUSAND) {
      return deprecatedCornerStore.creditLimit;
    }

    const sumCreditLimits = mergedCornerStore.creditLimit + deprecatedCornerStore.creditLimit;

    if (sumCreditLimits > TEN_THOUSAND) {
      return TEN_THOUSAND;
    }

    return sumCreditLimits;
  }

  private mergeInteresFactors(mergedUser: Users, deprecatedUser: Users) {
    const mergedInterests = {
      ...(mergedUser?.cornerStore?.interestFactors ?? {}),
      ...(deprecatedUser?.cornerStore?.interestFactors ?? {}),
    };

    return this.cornerStoreRepository.updateInterestFactor(mergedUser.id, mergedInterests);
  }

  async handler({ newUser, currentUserId }: { newUser: Users; currentUserId: string }) {
    const currentUser = await this.userRepository.findUserById(currentUserId);

    if (!currentUser) {
      throw new UserNotFoundException();
    }

    if (currentUser.status.name === UserStatus.MERGED) {
      throw new UserIsMergedException();
    }

    const { mergedUser, deprecatedUser, originalUser } = await this.mergeUsers(
      currentUser,
      newUser,
    );

    const mergedContacts = this.mergeContacts(mergedUser, deprecatedUser);

    await this.userRepository.updateVerificationData(mergedUser.id, mergedContacts);
    await this.userRepository.updatePhoneNumber(deprecatedUser, EMPTY_SPACE);

    await this.userRepository.updateNewMergedUserId({
      userId: deprecatedUser.id,
      newMergedUserId: mergedUser.id,
    });

    await this.mergeInteresFactors(mergedUser, deprecatedUser);

    this.eventEmitter.emit('account.merged', {
      mergedUser,
    });

    this.eventEmitter.emit('user.merged.migrate.history', {
      newUser: mergedUser,
      oldUser: deprecatedUser,
    });

    if (mergedUser.status.name !== UserStatus.VALIDATED) {
      if (originalUser.cornerStore.id !== mergedUser.cornerStore.id) {
        await this.manualUpdateCreditLimitService.handler(
          mergedUser.cornerStore.id,
          originalUser.cornerStore.creditLimit,
        );
      }
      return mergedUser;
    }

    const newCreditLimit = this.calculateNewCreditLimit({
      mergedCornerStore: mergedUser.cornerStore,
      deprecatedCornerStore: deprecatedUser.cornerStore,
      currentUser,
    });

    if (newCreditLimit !== mergedUser.cornerStore.creditLimit) {
      await this.manualUpdateCreditLimitService.handler(mergedUser.cornerStore.id, newCreditLimit);
    }

    this.eventEmitter.emit('account.upgrade', mergedUser.id);

    return mergedUser;
  }
}
