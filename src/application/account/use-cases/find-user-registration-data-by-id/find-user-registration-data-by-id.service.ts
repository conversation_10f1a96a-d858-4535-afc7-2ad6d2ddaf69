import { Injectable } from '@nestjs/common';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { UserNotFoundException } from '../../exceptions/user-not-found';
import { FindUserRegistrationDataByIdMapper } from '../../mappers/find-user-registration-data-by-id.mapper';
import { UserIsMergedException } from 'src/application/transaction/exceptions/user-is-merged';
import { Users } from 'src/infrastructure/database/models/users.entity';

@Injectable()
export class FindUserRegistrationDataByIdService {
  constructor(private readonly userRepository: UserRepository) {}

  private async manageMergedUser(user: Users) {
    if (!user.newMergedUser) {
      throw new UserIsMergedException();
    }

    return this.handler(user.newMergedUser.id);
  }

  async handler(userId: string) {
    const user = await this.userRepository.findUserRegistrationDataById(userId);

    if (!user) {
      throw new UserNotFoundException();
    }

    if (user.isMerged()) {
      return this.manageMergedUser(user);
    }

    return FindUserRegistrationDataByIdMapper.toResponse(user);
  }
}
