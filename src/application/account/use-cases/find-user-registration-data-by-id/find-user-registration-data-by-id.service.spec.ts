import { Test, TestingModule } from '@nestjs/testing';
import { FindUserRegistrationDataByIdService } from './find-user-registration-data-by-id.service';

describe('FindUserRegistrationDataByIdService', () => {
  let service: FindUserRegistrationDataByIdService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FindUserRegistrationDataByIdService],
    }).compile();

    service = module.get<FindUserRegistrationDataByIdService>(FindUserRegistrationDataByIdService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
