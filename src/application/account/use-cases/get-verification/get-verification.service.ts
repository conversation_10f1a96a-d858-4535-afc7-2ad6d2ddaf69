import { Injectable } from '@nestjs/common';
import { UserRepository } from '../../../../infrastructure/database/repositories/user.repository';
import { UserNotFoundException } from '../../exceptions/user-not-found';
import { GetAccountMapper } from '../../mappers/get-account.mapper';

@Injectable()
export class GetVerificationService {
  constructor(private readonly userRepository: UserRepository) {}

  public async handler(userId: string) {
    const user = await this.userRepository.findUserById(userId);

    if (!user) {
      throw new UserNotFoundException();
    }

    return GetAccountMapper.toResponse(user);
  }
}
