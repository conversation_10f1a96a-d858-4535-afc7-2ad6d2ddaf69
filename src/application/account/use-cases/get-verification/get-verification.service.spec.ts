import { Test, TestingModule } from '@nestjs/testing';
import { GetVerificationService } from './get-verification.service';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { createMock } from '@golevelup/ts-jest';

describe('GetVerificationService', () => {
  let service: GetVerificationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GetVerificationService,
        {
          provide: UserRepository,
          useValue: createMock<UserRepository>(),
        },
      ],
    }).compile();

    service = module.get<GetVerificationService>(GetVerificationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
