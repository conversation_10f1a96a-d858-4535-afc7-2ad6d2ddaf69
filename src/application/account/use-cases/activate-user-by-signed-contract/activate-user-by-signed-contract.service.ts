import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { WholesalerRepository } from 'src/infrastructure/database/repositories/wholesaler.repository';
import { ZapsignService } from 'src/infrastructure/external-services/zapsign/zapsign.service';
import { UserNotFoundException } from '../../exceptions/user-not-found';
import {
  FeatureFlags,
  UserBlockingSources,
  UserRegistrationConfirmationStatus,
  UserRiskFraudScores,
  UserStatus,
  WholesalerInformationStatus,
} from 'src/application/constants';
import { ContractInvalidFormatException } from '../../exceptions/contract-invalid-format';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { UserValidationResult } from 'src/infrastructure/database/models/user-validation-result';
import { FeatureFlagService } from 'src/infrastructure/feature-flag/feature-flag.service';

@Injectable()
export class ActivateUserBySignedContractService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly wholesalerRepository: WholesalerRepository,
    private readonly eventEmitter: EventEmitter2,
    private readonly zapSignService: ZapsignService,
    private readonly featureFlagService: FeatureFlagService,
  ) {}

  private async blockUser({
    userId,
    userToRegister,
    wholesalerName,
    verificationId,
    isSourceKycValidator = false,
    userValidationResult,
    isActiveBlockedUsersWithValidator = false,
  }: {
    userId: string;
    userToRegister: Users;
    wholesalerName: string;
    verificationId: string;
    isSourceKycValidator?: boolean;
    userValidationResult: UserValidationResult;
    isActiveBlockedUsersWithValidator?: boolean;
  }) {
    await this.wholesalerRepository.setBlockedStatusByUserId({ userId, isBlocked: true });
    await this.userRepository.updateUserStatus(userId, UserStatus.MANUAL_VALIDATION);
    await this.userRepository.setRegisterDate(userId, new Date());

    for (const wholesalerInformation of userToRegister.wholesalerInformations) {
      await this.wholesalerRepository.createUserIsBlockedHistory({
        wholesalerInformation,
        source: isSourceKycValidator
          ? UserBlockingSources.KYC_VALIDATOR
          : UserBlockingSources.USER_VALIDATOR,
      });
    }

    this.eventEmitter.emit('user.register.confirmation', {
      user: userToRegister,
      confirmationId: verificationId,
      status: UserRegistrationConfirmationStatus.MANUAL_VALIDATION,
      wholesalerName: wholesalerName,
    });

    this.eventEmitter.emit('account.blocked', {
      wholesalerName,
      user: userToRegister,
      cornerStore: userToRegister.cornerStore,
      phoneNumber: userToRegister.phoneNumber,
      fraudChecks: userValidationResult.fraudChecks,
      kycChecks: userValidationResult.kycChecks,
      isActiveBlockedUsersWithValidator,
    });
  }

  private async getIsBlocked(verificationResult: UserValidationResult, wholesalerName: string) {
    const isActiveBlockedUsersWithValidator =
      await this.featureFlagService.getFeatureFlagWithWholesaler(
        FeatureFlags.IS_ACTIVE_BLOCKED_USERS_WITH_VALIDATOR,
        wholesalerName,
      );

    if (!isActiveBlockedUsersWithValidator) {
      return false;
    }

    const isBlockedViaKyc =
      verificationResult.kycChecks &&
      verificationResult.kycChecks['score'] === UserRiskFraudScores.MAX_VALUE;

    const isBlockedViaFraudRisk =
      verificationResult.fraudChecks &&
      verificationResult.fraudChecks['fraudRiskScore'] === UserRiskFraudScores.MAX_VALUE;

    return isBlockedViaKyc || isBlockedViaFraudRisk;
  }

  async handler(notificationData: object) {
    if (!notificationData) {
      throw new Error('Notification data is required');
    }

    const { userId, wholesalerName, contractId, verificationId, userIdToNotify } =
      this.zapSignService.processSignedContractNotification(notificationData);

    if (!userId && !wholesalerName) {
      throw new ContractInvalidFormatException();
    }

    const user = await this.userRepository.findUserById(userId);

    if (!user) {
      throw new UserNotFoundException();
    }

    const wholesalerInformation = user.wholesalerInformations.find(
      (wholesalerInformation) => wholesalerInformation.wholesaler.name === wholesalerName,
    );

    if (user.status.name === UserStatus.PENDING_VALIDATION) {
      await this.userRepository.updateStatusToValidated(userId);
    }

    await this.wholesalerRepository.setWholesalerInformationStatus(
      userId,
      WholesalerInformationStatus.APPROVED,
    );

    await this.wholesalerRepository.updateWholesalerInformationMetadata(wholesalerInformation.id, {
      ...wholesalerInformation.data,
      contractId,
    });

    const verification = await this.userRepository.getVerificationbyId(verificationId);

    if (verification) {
      const [verificationResult] = verification.userValidationResults;

      const isBlocked = await this.getIsBlocked(verificationResult, wholesalerName);

      if (isBlocked) {
        await this.blockUser({
          userId,
          userToRegister: user,
          wholesalerName,
          verificationId,
          userValidationResult: verificationResult,
          isActiveBlockedUsersWithValidator: true,
        });
        return 'User blocked successfully';
      }
    }

    this.eventEmitter.emit('account.verificated', {
      user,
      cornerStore: user.cornerStore,
      phoneNumber: user.phoneNumber,
      wholesalerName: wholesalerInformation.wholesaler.name,
    });

    this.eventEmitter.emit('user.register', {
      user,
      wholesalerName: wholesalerInformation.wholesaler.name,
    });

    this.eventEmitter.emit('user.register.confirmation', {
      user: await this.userRepository.findUserById(userIdToNotify),
      confirmationId: contractId,
      status: UserRegistrationConfirmationStatus.APPROVED,
      wholesalerName: wholesalerName,
    });

    return 'User activated successfully';
  }
}
