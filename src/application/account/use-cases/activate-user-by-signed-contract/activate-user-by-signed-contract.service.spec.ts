import { Test, TestingModule } from '@nestjs/testing';
import { ActivateUserBySignedContractService } from './activate-user-by-signed-contract.service';

describe('ActivateUserBySignedContractService', () => {
  let service: ActivateUserBySignedContractService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ActivateUserBySignedContractService],
    }).compile();

    service = module.get<ActivateUserBySignedContractService>(ActivateUserBySignedContractService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
