import { Injectable, Logger } from '@nestjs/common';
import { UserValidationResult } from 'src/infrastructure/database/models/user-validation-result';
import { UserValidationResultRepository } from 'src/infrastructure/database/repositories/user-validation-result.repository';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { GetKycReportService } from 'src/middlewares/user-validator/use-cases/get-kyc-report/get-kyc-report.service';
import { SaveKycValidationResultService } from 'src/middlewares/user-validator/use-cases/save-kyc-validation-result/save-kyc-validation-result.service';

@Injectable()
export class SavePredictionService {
  logger = new Logger(SavePredictionService.name);
  constructor(
    private readonly saveKycValidationResultService: SaveKycValidationResultService,
    private readonly userValidationResultRepository: UserValidationResultRepository,
    private readonly getKycReportService: GetKycReportService,
    private readonly userRepository: UserRepository,
  ) {}

  private async processCompleteKyc(verification: any, userValidationResult: UserValidationResult) {
    const { score, reason, translatedReason, enrichment, information } =
      await this.getKycReportService.handler({
        verificationId: verification.id,
      });

    await this.userValidationResultRepository.addKycInformationToVerification(
      userValidationResult.id,
      {
        score,
        reason,
        translatedReason,
      },
    );

    await this.userRepository.updateAdditionalInformation(verification.user.id, {
      enrichment,
      information,
    });
  }

  async handler({
    verificationId,
    userId,
    prediction,
  }: {
    verificationId: string;
    userId: string;
    prediction: any;
  }) {
    await this.saveKycValidationResultService.handler({
      userId,
      verificationId: verificationId,
      dataRaw: {
        data: prediction,
      },
      requestId: prediction.request_id,
    });

    const verification = await this.userRepository.getVerificationbyId(verificationId);

    const userValidationResult = verification.userValidationResults
      .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
      .pop();

    try {
      await this.processCompleteKyc(verification, userValidationResult);
    } catch (error) {
      this.logger.error(error);
    }
  }
}
