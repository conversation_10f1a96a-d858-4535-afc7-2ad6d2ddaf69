import { Injectable, Logger } from '@nestjs/common';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { UserNotFoundException } from '../../exceptions/user-not-found';
import { PhoneNumberAlreadyExistsException } from '../../exceptions/phone-number-already-exist';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class UpdateUserService {
  logger = new Logger(UpdateUserService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async handler({
    userId,
    phoneNumber,
    wholesalerName,
  }: {
    userId: string;
    phoneNumber: string;
    wholesalerName: string;
  }) {
    const user = await this.userRepository.findUserByIdAndWholesaler(userId, wholesalerName);

    if (!user) {
      throw new UserNotFoundException();
    }

    const userWithSamePhoneNumber = await this.userRepository.getUserByPhoneNumber(phoneNumber);

    if (userWithSamePhoneNumber) {
      this.logger.error(
        `Phone number ${phoneNumber} already exists. Used by user ${userWithSamePhoneNumber.id}`,
      );
      throw new PhoneNumberAlreadyExistsException(userWithSamePhoneNumber.id);
    }

    await this.userRepository.updatePhoneNumberByWholesaler({
      user,
      phoneNumber,
      isConfirmed: true,
    });

    this.logger.log(
      `User ${userId} updated phone number to ${phoneNumber} by wholesaler ${wholesalerName}`,
    );

    this.eventEmitter.emit('user.updated', {
      user,
      phoneNumber,
      wholesalerName,
      oldPhoneNumber: user.phoneNumber,
    });

    return {
      userId,
      phoneNumber,
    };
  }
}
