import { Environments, Numbers, UserStatus, WholesalerNames } from 'src/application/constants';
import { WholesalerInformation } from '../../../infrastructure/database/models/wholesaler-information.entity';
import { Users } from 'src/infrastructure/database/models/users.entity';

export class FindUserByIdMapper {
  private static getStatus(user: Users, externalWholesaler: WholesalerInformation) {
    const userStatusArray: string[] = [UserStatus.IN_PAYMENT_DEFAULT, UserStatus.MANUAL_VALIDATION];

    if (userStatusArray.includes(user.status.name)) {
      return user.status.name;
    }

    return externalWholesaler.status.name;
  }

  private static getCreditLimitAvailable(user: Users, externalWholesaler: WholesalerInformation) {
    if (externalWholesaler.wholesaler.name !== WholesalerNames.CHIPER) {
      return user.cornerStore.getAvailableCreditLimit();
    }

    const activeTransactions = user.cornerStore.getActiveTransactions();

    if (
      activeTransactions.length >= Numbers.THREE &&
      process.env.NODE_ENV === Environments.PRODUCTION
    ) {
      return Numbers.ZERO;
    }

    return user.cornerStore.getAvailableCreditLimit();
  }

  static toResponse(user: Users, externalWholesaler: WholesalerInformation) {
    const creditLimitAvailable = this.getCreditLimitAvailable(user, externalWholesaler);

    return {
      userId: user.id,
      firstName: user.firstName,
      lastNames: user.lastNames,
      email: user.email,
      phoneNumber: user.phoneNumber,
      cornerStoreId: user.cornerStore.id,
      externalId: externalWholesaler.externalId,
      status: this.getStatus(user, externalWholesaler),
      creditLimitAvailable,
    };
  }
}
