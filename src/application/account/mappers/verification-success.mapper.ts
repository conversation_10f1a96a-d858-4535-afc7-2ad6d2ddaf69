import { Users } from 'src/infrastructure/database/models/users.entity';
import { UserVerificationCode } from '../../../infrastructure/database/models/user-verification-code.entity';

export class VerificationCodeMapper {
  static toResponse(verification: UserVerificationCode, user: Users) {
    return {
      verificationId: verification?.id,
      verificationStatus: 'User verified successfull',
      userId: user.id,
      cornerStoreId: user.cornerStore.id,
    };
  }
}
