import { UserStatus, WholesalerInformationStatus } from 'src/application/constants';
import { WholesalerInformation } from '../../../infrastructure/database/models/wholesaler-information.entity';
import { Users } from 'src/infrastructure/database/models/users.entity';

export class FindUserRegistrationDataByIdMapper {
  private static getStatus(user: Users, externalWholesaler: WholesalerInformation) {
    const userStatusArray: string[] = [UserStatus.IN_PAYMENT_DEFAULT, UserStatus.MANUAL_VALIDATION];

    if (userStatusArray.includes(user.status.name)) {
      return user.status.name;
    }

    return externalWholesaler.status.name;
  }

  private static getWholesalerInformation(user: Users) {
    const preApprovedWholeasler = user.wholesalerInformations.find(
      (wholesalerInformation) =>
        wholesalerInformation.status.name === WholesalerInformationStatus.PRE_APPROVED,
    );

    if (preApprovedWholeasler) {
      return preApprovedWholeasler;
    }

    const [wholesalerInformation] = user.wholesalerInformations;
    return wholesalerInformation;
  }

  static toResponse(user: Users) {
    const wholesalerInformation = this.getWholesalerInformation(user);

    return {
      userId: user.id,
      cornerStoreId: user.cornerStore.id,
      externalId: wholesalerInformation.externalId,
      phoneNumber: user.phoneNumber,
      firstName: user.firstName,
      lastNames: user.lastNames,
      email: user.email,
      status: this.getStatus(user, wholesalerInformation),
      creditLimitAvailable: user.cornerStore.getAvailableCreditLimit(),
      wholesaler: wholesalerInformation.wholesaler,
    };
  }
}
