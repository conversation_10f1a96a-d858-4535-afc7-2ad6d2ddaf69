import { Numbers } from 'src/application/constants';
import { WholesalerInformation } from 'src/infrastructure/database/models/wholesaler-information.entity';

export class BlockUsersByBatchMapper {
  static toResponse(wholesalerInformations: WholesalerInformation[]) {
    return {
      wholesaler: wholesalerInformations[Numbers.ZERO].wholesaler.name,
      blockedUsers: wholesalerInformations.map((wholesalerInformation) => ({
        userId: wholesalerInformation.user.id,
        externalId: wholesalerInformation.externalId,
      })),
    };
  }
}
