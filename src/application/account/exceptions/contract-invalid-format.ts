import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorCodes } from '../../../infrastructure/constants';

export class ContractInvalidFormatException extends HttpException {
  constructor() {
    super(
      {
        errorCode: ErrorCodes.CONTRACT_INVALID_FORMAT.code,
        errorMessage: ErrorCodes.CONTRACT_INVALID_FORMAT.message,
      },
      HttpStatus.UNPROCESSABLE_ENTITY,
    );
  }
}
