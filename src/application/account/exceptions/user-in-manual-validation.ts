import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorCodes } from '../../../infrastructure/constants';

export class UserInManualValidationException extends HttpException {
  constructor() {
    super(
      {
        errorCode: ErrorCodes.USER_IN_MANUAL_VALIDATION.code,
        errorMessage: ErrorCodes.USER_IN_MANUAL_VALIDATION.message,
      },
      HttpStatus.UNPROCESSABLE_ENTITY,
    );
  }
}
