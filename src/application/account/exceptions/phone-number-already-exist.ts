import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorCodes } from '../../../infrastructure/constants';

export class PhoneNumberAlreadyExistsException extends HttpException {
  constructor(userIdWithPhoneNumber: string) {
    super(
      {
        errorCode: ErrorCodes.PHONE_NUMBER_ALREADY_EXISTS.code,
        errorMessage: `${ErrorCodes.PHONE_NUMBER_ALREADY_EXISTS.message}. Used by user with id: ${userIdWithPhoneNumber}`,
      },
      HttpStatus.BAD_REQUEST,
    );
  }
}
