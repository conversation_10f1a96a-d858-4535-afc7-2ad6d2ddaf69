import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorCodes } from '../../../infrastructure/constants';

export class PhoneNumberRequiredException extends HttpException {
  constructor() {
    super(
      {
        errorCode: ErrorCodes.PHONE_NUMBER_IS_REQUIRED.code,
        errorMessage: ErrorCodes.PHONE_NUMBER_IS_REQUIRED.message,
      },
      HttpStatus.UNPROCESSABLE_ENTITY,
    );
  }
}
