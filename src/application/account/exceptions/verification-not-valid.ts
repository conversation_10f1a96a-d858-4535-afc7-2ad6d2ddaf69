import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorCodes } from '../../../infrastructure/constants';

export class VerificationNotValidException extends HttpException {
  constructor() {
    super(
      {
        errorCode: ErrorCodes.VERIFICATION_NOT_VALID.code,
        errorMessage: ErrorCodes.VERIFICATION_NOT_VALID.message,
      },
      HttpStatus.UNPROCESSABLE_ENTITY,
    );
  }
}
