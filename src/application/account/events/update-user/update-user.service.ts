import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { EMPTY_SPACE, Environments } from 'src/application/constants';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { SlackService } from 'src/infrastructure/external-services/slack/slack.service';
import { WhatsappService } from 'src/infrastructure/external-services/whatsapp/whatsapp.service';

@Injectable()
export class UpdateUserEvent {
  private logger = new Logger(UpdateUserEvent.name);
  constructor(
    private readonly slackService: SlackService,
    private readonly whatsappService: WhatsappService,
  ) {}

  @OnEvent('user.updated', { async: false })
  async handleUserUpdatedEvent({
    user,
    phoneNumber,
    wholesalerName,
    oldPhoneNumber,
  }: {
    user: Users;
    phoneNumber: string;
    wholesalerName: string;
    oldPhoneNumber: string;
  }) {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should send a message to slack, but you are in ${process.env.NODE_ENV} env`,
      );
    }

    await this.slackService.sendMessage(
      '☎️ *Cambio de número de teléfono* ☎️ \n' +
        `*Nombre:* ${user.firstName} ${user.lastNames || EMPTY_SPACE}\n` +
        `*Teléfono anterior:* ${user.phoneNumber}\n` +
        `*Teléfono nuevo:* ${phoneNumber}\n` +
        `*Distribuidor:* ${wholesalerName}\n` +
        `<https://backoffice.propaga.mx/corner-store/${user.cornerStore.id}/v2|Ver en backoffice>`,
    );

    this.whatsappService.flowExecution(oldPhoneNumber, {
      flowsId: WhatsappService.WhatsappFlows.CHANGE_PHONE_NUMBER,
      parameters: {
        oldPhoneNumber,
        newPhoneNumber: phoneNumber,
      },
    });
  }
}
