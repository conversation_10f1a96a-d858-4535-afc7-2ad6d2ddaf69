import { Test, TestingModule } from '@nestjs/testing';
import { UpdateUserEvent } from './update-user.service';

describe('UpdateUserService', () => {
  let service: UpdateUserEvent;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UpdateUserEvent],
    }).compile();

    service = module.get<UpdateUserEvent>(UpdateUserEvent);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
