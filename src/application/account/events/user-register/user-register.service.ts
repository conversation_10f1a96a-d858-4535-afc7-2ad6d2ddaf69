import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import {
  EMPTY_SPACE,
  Environments,
  FeatureFlags,
  interestFactorByWholesaler,
} from 'src/application/constants';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { CornerStoreRepository } from 'src/infrastructure/database/repositories/corner-store.repository';
import { PaymentInformationRepository } from 'src/infrastructure/database/repositories/payment-information.repository';
import { AlgoliaService } from 'src/infrastructure/external-services/algolia/algolia.service';
import { FeatureFlagService } from 'src/infrastructure/feature-flag/feature-flag.service';
import { StatusTranslator } from 'src/utils/status-translator';

@Injectable()
export class UserRegisterService {
  private logger = new Logger(UserRegisterService.name);
  constructor(
    private readonly paymentInformationRepository: PaymentInformationRepository,
    private readonly algoliaService: AlgoliaService,
    private readonly cornerStoreRepository: CornerStoreRepository,
    private readonly featureFlagService: FeatureFlagService,
  ) {}

  async createRecordInAlgolia(user: Users) {
    const userIndexSearch = `user_${process.env.NODE_ENV}`;

    const externalIds = user.wholesalerInformations.map(
      (wholesalerInformation) => wholesalerInformation.externalId,
    );

    const wholesalers = user.wholesalerInformations.map(
      (wholesalerInformation) => wholesalerInformation.wholesaler.name,
    );

    await this.algoliaService.addRecordToIndex(userIndexSearch, {
      ...user,
      fullName: `${user.firstName} ${user.lastNames || EMPTY_SPACE}`,
      status: {
        ...user.status,
        translatedStatusName: StatusTranslator.translateUserStatus(user.status.name),
      },
      externalIds,
      wholesalers,
      objectID: user.cornerStore.id,
      isUserInDefault: false,
      isUserInPaymentPlan: false,
      isActive: true,
      isNew: true,
      isBlocked: false,
      signUpStatus: StatusTranslator.translateSignUpStatus(user.signUpStatus?.name),
    });

    this.logger.log(`User ${user.id} added to index ${userIndexSearch}`);
  }

  async updateInterestFactorByUser(user: Users, wholesalerName: string) {
    const factor = interestFactorByWholesaler[wholesalerName.toLowerCase() ?? 'default'];

    await this.cornerStoreRepository.updateInterestFactor(user.cornerStore.id, {
      [wholesalerName.toLowerCase()]: factor,
    });
    this.logger.log(
      `Set interest factor to cornerStore: ${user.cornerStore.id} with ${wholesalerName}: ${factor}`,
    );
  }

  @OnEvent('user.register', { async: false })
  async handleUserRegisterEvent({ user, wholesalerName }: { user: Users; wholesalerName: string }) {
    if (process.env.NODE_ENV === Environments.TESTING) {
      this.logger.log(
        `It should create a clabe and save record in algolia, but you are in ${process.env.NODE_ENV} env`,
      );
      return;
    }

    await this.createRecordInAlgolia(user);
    await this.updateInterestFactorByUser(user, wholesalerName);

    const isActiveClabeAssignation = await this.featureFlagService.getFeatureFlag(
      FeatureFlags.IS_ACTIVE_CLABE_ASSIGNATION,
    );

    if (!isActiveClabeAssignation) {
      this.logger.log(`It should create a clabe, but is not active for ${wholesalerName}`);
      return;
    }

    const paymentInformation = await this.paymentInformationRepository.assignClabe(user);
    this.logger.log(`Payment information assigned: ${paymentInformation.clabe} to user ${user.id}`);
  }
}
