import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { CornerStore } from 'src/infrastructure/database/models/corner-store.entity';
import { EMPTY_SPACE, Environments, interestFactorByWholesaler } from 'src/application/constants';
import { WhatsappService } from 'src/infrastructure/external-services/whatsapp/whatsapp.service';
import { StringOperations } from 'src/utils/string-operations';
import { UserValidatorService } from 'src/middlewares/user-validator/user-validator.service';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';

@Injectable()
export class CreatedVerificationEvent {
  constructor(
    private readonly whatsappService: WhatsappService,
    private readonly userRepository: UserRepository,
    private readonly userValidatorService: UserValidatorService,
  ) {}

  @OnEvent('account.verificated', { async: false })
  async handleCreatedVerificationEvent({
    phoneNumber,
    wholesalerName,
    cornerStore,
    user,
  }: {
    phoneNumber: string;
    cornerStore: CornerStore;
    wholesalerName: string;
    user: Users;
  }) {
    const wholesalerLowered = wholesalerName.toLowerCase();

    const interestFactor =
      interestFactorByWholesaler[wholesalerLowered] ?? interestFactorByWholesaler['default'];

    await this.whatsappService.flowExecution(phoneNumber, {
      flowsId: WhatsappService.WhatsappFlows.WELCOME_MESSAGE_ON_REGISTER,
      parameters: {
        interes: interestFactor.toString(),
        credito: StringOperations.numberToFormattedString(cornerStore.creditLimit),
        socio: wholesalerName,
        nombre:
          process.env.NODE_ENV !== Environments.PRODUCTION
            ? `(PRUEBAS) ${user.firstName}`
            : user.firstName,
        apellido: user.lastNames || EMPTY_SPACE,
        backoffice: `https://backoffice.propaga.mx/corner-store/${cornerStore.id}`,
      },
    });

    await this.userValidatorService.saveLocation({
      userId: user.id,
      phoneNumber,
      latitude: Number(cornerStore.location['latitude']),
      longitude: Number(cornerStore.location['longitude']),
      name: `${user.firstName} ${user.lastNames}`,
    });

    await this.userRepository.upsertUserDetail(user.id, {
      metadata: { showPortalV2: true },
    });
  }
}
