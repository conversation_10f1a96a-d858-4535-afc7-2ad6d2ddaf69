import { Test, TestingModule } from '@nestjs/testing';
import { CreatedVerificationEvent } from './created-verification.service';

describe('CreatedVerificationService', () => {
  let service: CreatedVerificationEvent;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CreatedVerificationEvent],
    }).compile();

    service = module.get<CreatedVerificationEvent>(CreatedVerificationEvent);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
