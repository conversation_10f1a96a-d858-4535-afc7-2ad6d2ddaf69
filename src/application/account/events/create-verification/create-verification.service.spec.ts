import { Test, TestingModule } from '@nestjs/testing';
import { CreateVerificationService } from './create-verification.service';

describe('CreateVerificationService', () => {
  let service: CreateVerificationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CreateVerificationService],
    }).compile();

    service = module.get<CreateVerificationService>(CreateVerificationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
