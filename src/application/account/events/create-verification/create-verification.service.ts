import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { WhatsappService } from 'src/infrastructure/external-services/whatsapp/whatsapp.service';
import { WhatsappTemplates } from 'src/infrastructure/external-services/whatsapp/templates';
@Injectable()
export class CreateVerificationEvent {
  private logger = new Logger(CreateVerificationEvent.name);
  constructor(private readonly whatsappService: WhatsappService) {}

  @OnEvent('account.verification.created', { async: false })
  async handleAccountVerificationCreatedEvent({
    verificationCode,
    phoneNumber,
  }: {
    verificationCode: string;
    phoneNumber: string;
  }) {
    const whatsappResponse = await this.whatsappService.sendMessageByTemplate(phoneNumber, {
      template: WhatsappTemplates.OTP_VERIFICATION,
      parameters: [verificationCode],
    });

    this.logger.log(
      `Send whatsapp message ${phoneNumber} with messageId ${whatsappResponse.data.id}`,
    );
  }
}
