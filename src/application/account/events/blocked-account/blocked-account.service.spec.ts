import { Test, TestingModule } from '@nestjs/testing';
import { BlockedAccountService } from './blocked-account.service';

describe('BlockedAccountService', () => {
  let service: BlockedAccountService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [BlockedAccountService],
    }).compile();

    service = module.get<BlockedAccountService>(BlockedAccountService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
