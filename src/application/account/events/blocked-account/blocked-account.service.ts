import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import {
  EMPTY_SPACE,
  Environments,
  SlackChannels,
  UserRiskFraudScores,
} from 'src/application/constants';
import { WhatsappService } from 'src/infrastructure/external-services/whatsapp/whatsapp.service';
import { UserValidatorService } from 'src/middlewares/user-validator/user-validator.service';
import { CornerStore } from 'src/infrastructure/database/models/corner-store.entity';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { SlackService } from 'src/infrastructure/external-services/slack/slack.service';
import { FraudChecks } from 'src/middlewares/user-validator/interfaces/user-fraud-risk-result';

@Injectable()
export class BlockedAccountService {
  private logger = new Logger(BlockedAccountService.name);
  constructor(
    private readonly whatsappService: WhatsappService,
    private readonly userValidatorService: UserValidatorService,
    private readonly slackService: SlackService,
  ) {}

  getErrorMessage(reason: string, fraudRiskScore: number) {
    const isBlocked = fraudRiskScore === UserRiskFraudScores.MAX_VALUE;

    switch (reason) {
      case 'INVALID_NAME':
        return '- Cliente con nombre inválido';
      case 'CONTACT_INVALID_NAME':
        return '- Contacto con nombre inválido';
      case 'INVALID_DISTANCE_BETWEEN_STORES':
        return '- Hay un negocio cercano que se encuentra registrado en Propaga';
      case 'INVALID_DISTANCE_BETWEEN_STORE_IN_DEFAULT':
        return '- Hay un negocio cercano registrado en Propaga que se encuentra en mora';
      case 'CONTACT_INVALID_WHATSAPP':
        return isBlocked
          ? '- Contactos no cuentan con Whatsapp'
          : '- Contacto no cuenta con Whatsapp';
      case 'INVALID_PHONE_NUMBER_PHONE_ACTIVE':
        return isBlocked
          ? '- Teléfonos de los contactos no están activos'
          : '- Teléfono de un contacto no está activo';
      case 'INVALID_PHONE_NUMBER_PHONE_PORTED_SINCE_X_DAYS':
        return isBlocked
          ? '- Teléfonos de los contactos portados recientemente'
          : '- Teléfono de un contacto portado recientemente';
      case 'INVALID_PHONE_NUMBER_PHONE_CARRIER':
        return isBlocked
          ? '- Teléfonos de los contactos no tienen compañía'
          : '- Teléfono de un contacto no tiene compañía';
      case 'INVALID_PHONE_NUMBER_PHONE_LINE_TYPE':
        return isBlocked
          ? '- Teléfonos de los contactos no tienen línea'
          : '- Teléfono de un contacto no tiene línea';
      case 'CONTACT_ALREADY_REGISTERED':
        return '- Contacto registrado en Propaga';
      case 'CONTACT_ALREADY_REGISTERED_IN_DEFAULT':
        return '- Contacto registrado en Propaga se encuentra en mora';
      case 'CONTACT_IS_AUTORREFERED':
        return '- Contacto es auto referido';
      case 'USER_IN_DEFAULT_WITH_ANOTHER_WHOLESALER':
        return '- Cliente se encuentra en mora con otro distribuidor';
      case 'USER_IS_UNVERIFIED_BY_WHOLESALER':
        return '- Cliente no verificado por distribuidor';
      case 'CONTACT_BLACKLISTED_NUMBER':
        return '- Teléfono de contacto en lista negra';
      case 'PHONE_NUMBER_IS_BLACKLISTED':
        return '- Teléfono del cliente en lista negra';
      default:
        this.logger.error(reason);
        return `- Error desconocido (${reason})`;
    }
  }

  private getFraudChecksMessageText(fraudChecks: FraudChecks) {
    if (!fraudChecks) {
      return EMPTY_SPACE;
    }

    const { validations } = fraudChecks;
    const reasons = validations.filter((reason) => reason.score > UserRiskFraudScores.OK_VALUE);

    const reasonMessages = reasons.map(({ errorCode, score }) =>
      this.getErrorMessage(errorCode, score),
    );

    return reasonMessages.join('\n');
  }

  private getKycChecksMessageText(kycChecks: any) {
    if (!kycChecks) {
      return EMPTY_SPACE;
    }

    const { score, reason } = kycChecks;

    if (score === UserRiskFraudScores.OK_VALUE) {
      return EMPTY_SPACE;
    }

    return reason.map((reason: string) => `- ${reason} \n`).join('');
  }

  private getMessageTitle(isBlocking: boolean, isActiveBlockedUsersWithValidator: boolean) {
    if (isBlocking) {
      return `:lock: * ${
        isActiveBlockedUsersWithValidator ? 'Cliente bloqueado' : 'Cliente debería ser bloqueado'
      }* :lock: \n`;
    }

    return ':checkered_flag: *Cliente en revisión* :checkered_flag: \n';
  }

  private async sendMessage({
    phoneNumber,
    wholesalerName,
    fraudChecks,
    user,
    isBlocking,
    isActiveBlockedUsersWithValidator,
    kycChecks,
  }: {
    phoneNumber: string;
    wholesalerName: string;
    fraudChecks: any;
    user: Users;
    isBlocking: boolean;
    isActiveBlockedUsersWithValidator: boolean;
    kycChecks: any;
  }) {
    if (isBlocking && isActiveBlockedUsersWithValidator) {
      await this.whatsappService.flowExecution(phoneNumber, {
        flowsId: WhatsappService.WhatsappFlows.USER_BLOCKED,
        parameters: {
          distribuidor: wholesalerName,
        },
      });

      this.logger.log(`Send blocked message to user with phone number ${phoneNumber}`);
    }

    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should send a message to slack #fraud-validation-alerts, but you are in ${process.env.NODE_ENV} env`,
      );
    }

    const title = this.getMessageTitle(isBlocking, isActiveBlockedUsersWithValidator);
    const fraudReasonMessages = this.getFraudChecksMessageText(fraudChecks);
    const kycReasonMessages = this.getKycChecksMessageText(kycChecks);

    await this.slackService.sendMessage(
      title +
        `Nombre: ${user.firstName} ${user.lastNames}\n` +
        `Teléfono: ${user.phoneNumber}\n` +
        `Distribuidor: ${wholesalerName}\n` +
        `Razón(es): \n${fraudReasonMessages}\n${kycReasonMessages}\n` +
        `<https://backoffice.propaga.mx/corner-store/${user.cornerStore.id}/v2|Ver en backoffice>`,
      SlackChannels.FRAUD_VALIDATION_ALERTS,
    );
  }

  @OnEvent('account.blocked', { async: false })
  async handleBlockedAccountEvent({
    phoneNumber,
    wholesalerName,
    cornerStore,
    user,
    fraudChecks,
    isActiveBlockedUsersWithValidator,
    kycChecks,
  }: {
    phoneNumber: string;
    cornerStore: CornerStore;
    wholesalerName: string;
    user: Users;
    fraudChecks: any;
    isActiveBlockedUsersWithValidator: boolean;
    kycChecks: any;
  }) {
    if (isActiveBlockedUsersWithValidator) {
      await this.userValidatorService.saveLocation({
        userId: user.id,
        phoneNumber,
        latitude: Number(cornerStore.location['latitude']),
        longitude: Number(cornerStore.location['longitude']),
        name: `${user.firstName} ${user.lastNames}`,
      });
    }

    const isBlockingByFraudRiskScore =
      fraudChecks && fraudChecks['fraudRiskScore'] === UserRiskFraudScores.MAX_VALUE;

    const isBlockingByKycScore = kycChecks && kycChecks['score'] === UserRiskFraudScores.MAX_VALUE;

    return this.sendMessage({
      phoneNumber,
      wholesalerName,
      fraudChecks,
      kycChecks,
      user,
      isBlocking: isBlockingByFraudRiskScore || isBlockingByKycScore,
      isActiveBlockedUsersWithValidator,
    });
  }
}
