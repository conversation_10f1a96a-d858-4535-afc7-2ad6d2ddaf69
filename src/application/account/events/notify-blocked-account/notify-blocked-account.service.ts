import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { FeatureFlags } from 'src/application/constants';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { N8nService } from 'src/infrastructure/external-services/n8n/n8n.service';
import { FeatureFlagService } from 'src/infrastructure/feature-flag/feature-flag.service';

@Injectable()
export class NotifyBlockedAccountService {
  logger = new Logger(NotifyBlockedAccountService.name);
  constructor(
    private readonly n8nService: N8nService,
    private readonly featureFlagService: FeatureFlagService,
  ) {}

  @OnEvent('notify.account-blocked', { async: false })
  async handleNotifyBlockedAccountEvent({
    user,
    wholesalerName,
    fraudChecks,
    kycChecks,
  }: {
    user: Users;
    wholesalerName: string;
    fraudChecks: any;
    kycChecks: any;
  }) {
    try {
      if (kycChecks) {
        this.logger.log(`Should send message to n8n but user already made the KYC validation`);
        return;
      }

      const isActiveSendNotification = await this.featureFlagService.getFeatureFlagWithWholesaler(
        FeatureFlags.IS_SEND_BLOCKED_FORM_ACTIVE,
        wholesalerName,
      );

      if (!isActiveSendNotification) {
        this.logger.log(
          `Feature flag IS_SEND_BLOCKED_FORM_ACTIVE is disabled for wholesaler ${wholesalerName}`,
        );
        return;
      }

      const response = await this.n8nService.notifyRabbitBlockedAccount({
        user,
        wholesalerName,
        fraudChecks,
      });

      this.logger.log(`Send message to n8n: ${response}`);
    } catch (error) {
      this.logger.error(`Error sending message to n8n: ${error}`);
    }
  }
}
