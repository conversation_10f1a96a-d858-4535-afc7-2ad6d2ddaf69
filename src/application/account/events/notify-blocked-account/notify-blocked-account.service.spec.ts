import { Test, TestingModule } from '@nestjs/testing';
import { NotifyBlockedAccountService } from './notify-blocked-account.service';

describe('NotifyBlockedAccountService', () => {
  let service: NotifyBlockedAccountService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [NotifyBlockedAccountService],
    }).compile();

    service = module.get<NotifyBlockedAccountService>(NotifyBlockedAccountService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
