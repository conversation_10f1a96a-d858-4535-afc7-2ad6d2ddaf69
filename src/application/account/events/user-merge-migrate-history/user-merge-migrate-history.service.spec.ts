import { Test, TestingModule } from '@nestjs/testing';
import { UserMergeMigrateHistoryServiceEvent } from './user-merge-migrate-history.service';

describe('UserMergeMigrateHistoryService', () => {
  let service: UserMergeMigrateHistoryServiceEvent;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UserMergeMigrateHistoryServiceEvent],
    }).compile();

    service = module.get<UserMergeMigrateHistoryServiceEvent>(UserMergeMigrateHistoryServiceEvent);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
