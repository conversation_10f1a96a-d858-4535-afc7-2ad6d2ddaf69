import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { DefaultPenalty } from 'src/infrastructure/database/models/default-penalty.entity';
import { PaymentPlan } from 'src/infrastructure/database/models/payment-plan.entity';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { DefaultPenaltyRepository } from 'src/infrastructure/database/repositories/default-penalty.repository';
import { PaymentPlanRepository } from 'src/infrastructure/database/repositories/payment-plan.repository';

@Injectable()
export class UserMergeMigrateHistoryServiceEvent {
  constructor(
    private readonly defaultPenaltyRepository: DefaultPenaltyRepository,
    private readonly paymentPlanRepository: PaymentPlanRepository,
  ) {}

  private async migrateDefaultPenalties(userId: string, defaultPenalties: DefaultPenalty[]) {
    if (!defaultPenalties.length) {
      return;
    }

    for (const { id } of defaultPenalties) {
      await this.defaultPenaltyRepository.reassignDefaultPenaltyToUser(id, userId);
    }
  }

  private async migratePaymentPlans(userId: string, paymentPlans: PaymentPlan[]) {
    if (!paymentPlans.length) {
      return;
    }

    for (const { id } of paymentPlans) {
      await this.paymentPlanRepository.reassingPaymentPlanToUser(id, userId);
    }
  }

  @OnEvent('user.merged.migrate.history', { async: false })
  async handleUserMergedMigrateHistoryEvent({
    newUser,
    oldUser,
  }: {
    newUser: Users;
    oldUser: Users;
  }) {
    await this.migrateDefaultPenalties(newUser.id, oldUser.defaultPenalties);
    await this.migratePaymentPlans(newUser.id, oldUser.paymentPlans);
  }
}
