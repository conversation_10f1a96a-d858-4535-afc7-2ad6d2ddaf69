import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import {
  FormOneTransactionPaidRule,
  UserPaid3TransactionsRule,
  ValidationCallAndFormRule,
  ValidationCallAndOneTransactionPaidRule,
} from './rules';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { FeatureFlagService } from 'src/infrastructure/feature-flag/feature-flag.service';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { CornerStoreRepository } from 'src/infrastructure/database/repositories/corner-store.repository';
import { Offer } from 'src/infrastructure/database/models/offer.entity';
import { FeatureFlags, SlackChannels, UserSignUpStatusEnum } from 'src/application/constants';
import { UserHasMultipleWholesalersAndPaidRule } from './rules/user-has-multiple-wholesalers-and-paid';
import { OfferRepository } from 'src/infrastructure/database/repositories/offer.repository';
import { SlackService } from 'src/infrastructure/external-services/slack/slack.service';
import { KycCompletedRule } from './rules/kyc-completed';

@Injectable()
export class UpgradeAccountService {
  logger = new Logger(UpgradeAccountService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly featureFlagService: FeatureFlagService,
    private readonly cornerStoreRepository: CornerStoreRepository,
    private readonly offerRepository: OfferRepository,
    private readonly slackService: SlackService,
    private readonly eventEmmitter: EventEmitter2,
  ) {}

  private executeSideEffects(user: Users, rulesIsSatisfied: any) {
    const promises = [
      this.userRepository.updateSignUpStatus(user.id, UserSignUpStatusEnum.COMPLETED),
      this.userRepository.upsertUserDetail(user.id, {
        activationReason: rulesIsSatisfied
          .map((rule: { reason: string }) => rule.reason)
          .join(', '),
      }),
      this.eventEmmitter.emit('user.updateSearch', {
        user,
        flags: { signUpStatus: UserSignUpStatusEnum.COMPLETED },
      }),
    ] as any[];

    if (rulesIsSatisfied.length > 1) {
      promises.push(
        this.slackService.sendMessage(
          `:zap: *Usuario actualizado* :zap: \n` +
            `Nombre: ${user.firstName} ${user.lastNames} \n` +
            `Teléfono: ${user.phoneNumber} \n` +
            `Distribuidor: ${user.wholesalerInformations[0].wholesaler.name} \n` +
            `Motivos de la actualización: \n` +
            `${rulesIsSatisfied.map((rule: { reason: string }) => `- ${rule.reason}`).join('\n')}`,
          SlackChannels.FRAUD_VALIDATION_ALERTS,
        ),
      );
    }

    return Promise.all(promises);
  }

  private async enabledFullCreditLimit(user: Users) {
    const [offer] = user.wholesalerInformations
      .map((wholesalerInformation) => {
        if (!wholesalerInformation.offers) {
          return [] as Offer[];
        }
        return wholesalerInformation.offers.find((offer) => offer.isActive);
      })
      .flat();

    if (offer && offer.atp) {
      if (user.cornerStore.creditLimit >= offer.atp) {
        await this.offerRepository.markAsCompleted(offer.id);
        return;
      }

      await this.cornerStoreRepository.updateCreditLimit(user.cornerStore, offer.atp);
      await this.offerRepository.markAsCompleted(offer.id);
    }
  }
  private async validateFeatureFlag(): Promise<boolean> {
    const featureFlags = await this.featureFlagService.getFeatureFlag(
      FeatureFlags.IS_ACTIVE_CARROT_PROJECT,
    );

    return featureFlags;
  }

  private async validateUserIsSatisfied(
    rules: (
      | FormOneTransactionPaidRule
      | UserPaid3TransactionsRule
      | ValidationCallAndFormRule
      | ValidationCallAndOneTransactionPaidRule
      | UserHasMultipleWholesalersAndPaidRule
      | KycCompletedRule
    )[],
    user: Users,
    userId: string,
  ) {
    const rulesIsSatisfied = rules
      .map((rule) => rule.isSatisfied(user))
      .filter((rule) => rule.isSatisfied);

    if (rulesIsSatisfied.length > 0) {
      await this.executeSideEffects(user, rulesIsSatisfied);
      await this.enabledFullCreditLimit(user);
      this.logger.log(`User ${userId} upgraded account successfully`);
    }
  }

  @OnEvent('account.upgrade', { async: false })
  async handler(userId: string) {
    const user = await this.userRepository.findUserByIdWithOffers(userId);

    const isActiveUserUpgrade = await this.validateFeatureFlag();

    const kycRule = [new KycCompletedRule()];
    await this.validateUserIsSatisfied(kycRule, user, userId);

    if (!isActiveUserUpgrade) {
      this.logger.log(`User upgrade is not active for this user, not upgrade account`);
      return;
    }

    if (user.isBlocked()) {
      this.logger.log(`User ${userId} is blocked, not upgrade account`);
      return;
    }

    if (user.isSignUpValidated()) {
      this.logger.log(`User ${userId} is sign-up validated, not upgrade account`);
      return;
    }

    const rules = [
      new FormOneTransactionPaidRule(),
      new UserPaid3TransactionsRule(),
      new ValidationCallAndFormRule(),
      new ValidationCallAndOneTransactionPaidRule(),
      new UserHasMultipleWholesalersAndPaidRule(),
    ];

    await this.validateUserIsSatisfied(rules, user, userId);
  }
}
