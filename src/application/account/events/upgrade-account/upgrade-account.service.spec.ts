import { Test, TestingModule } from '@nestjs/testing';
import { UpgradeAccountService } from './upgrade-account.service';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { UserStatus } from 'src/application/constants';

describe('UpgradeAccountService', () => {
  let service: UpgradeAccountService;
  let userRepository: jest.Mocked<UserRepository>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpgradeAccountService,
        {
          provide: UserRepository,
          useFactory: () => ({
            findUserById: jest.fn(),
            updateSignUpStatusToCompleted: jest.fn(),
            updateSignUpStatus: jest.fn(),
            upsertUserDetail: jest.fn(),
            updateUserStatus: jest.fn(),
          }),
        },
      ],
    }).compile();

    service = module.get<UpgradeAccountService>(UpgradeAccountService);
    userRepository = module.get(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('handler', () => {
    it('should not upgrade blocked user', async () => {
      const mockUser = {
        isBlocked: jest.fn().mockReturnValue(true),
        isSignUpValidated: jest.fn().mockReturnValue(false),
      } as any;
      userRepository.findUserById.mockResolvedValue(mockUser);

      await service.handler('user-id');

      expect(userRepository.updateSignUpStatus).not.toHaveBeenCalled();
      expect(userRepository.upsertUserDetail).not.toHaveBeenCalled();
      expect(userRepository.updateUserStatus).not.toHaveBeenCalled();
    });

    it('should not upgrade already validated user', async () => {
      const mockUser = {
        isBlocked: jest.fn().mockReturnValue(false),
        isSignUpValidated: jest.fn().mockReturnValue(true),
      } as any;
      userRepository.findUserById.mockResolvedValue(mockUser);

      await service.handler('user-id');

      expect(userRepository.updateSignUpStatus).not.toHaveBeenCalled();
      expect(userRepository.upsertUserDetail).not.toHaveBeenCalled();
      expect(userRepository.updateUserStatus).not.toHaveBeenCalled();
    });

    it('should upgrade user when rules are satisfied', async () => {
      const mockUser = {
        isBlocked: jest.fn().mockReturnValue(false),
        isSignUpValidated: jest.fn().mockReturnValue(false),
        cornerStore: {
          getPaidTransactionsInTime: jest.fn().mockReturnValue([1, 2, 3]),
        },
        userDetail: {
          kycFormCompletionDate: new Date(),
          activationCallCompletionDate: new Date(),
        },
      } as any;
      userRepository.findUserById.mockResolvedValue(mockUser);

      await service.handler('user-id');

      expect(userRepository.updateSignUpStatus).toHaveBeenCalledWith('user-id');
      expect(userRepository.upsertUserDetail).toHaveBeenCalledWith('user-id', {
        activationReason:
          'KYC form completed and one transaction paid, 3 transactions paid in time, Validation call and KYC form completed, Validation call and 1 transaction paid in time',
      });
      expect(userRepository.updateUserStatus).toHaveBeenCalledWith('user-id', UserStatus.VALIDATED);
    });

    it('should not upgrade user when no rules are satisfied', async () => {
      const mockUser = {
        isBlocked: jest.fn().mockReturnValue(false),
        isSignUpValidated: jest.fn().mockReturnValue(false),
        cornerStore: {
          getPaidTransactionsInTime: jest.fn().mockReturnValue([]),
        },
        userDetail: {
          kycFormCompletionDate: null,
          activationCallCompletionDate: null,
        },
      } as any;
      userRepository.findUserById.mockResolvedValue(mockUser);

      await service.handler('user-id');

      expect(userRepository.updateSignUpStatus).not.toHaveBeenCalled();
      expect(userRepository.upsertUserDetail).not.toHaveBeenCalled();
      expect(userRepository.updateUserStatus).not.toHaveBeenCalled();
    });
  });
});
