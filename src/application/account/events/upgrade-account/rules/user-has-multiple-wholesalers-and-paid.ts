import { Users } from 'src/infrastructure/database/models/users.entity';
import { IRule } from './IRule';

const MINIMAL_TRANSACTIONS_TO_UPGRADE = 1;

export class UserHasMultipleWholesalersAndPaidRule implements IRule {
  isSatisfied(user: Users) {
    const paidTransactionsInTime = user.cornerStore.getPaidTransactionsInTime(user);
    const wholesalers = user.wholesalerInformations.map(
      (wholesalerInformation) => wholesalerInformation.wholesaler.name,
    );
    return {
      isSatisfied:
        wholesalers.length > 1 && paidTransactionsInTime.length >= MINIMAL_TRANSACTIONS_TO_UPGRADE,
      reason: '1 transaction paid in time and user with multiple wholesalers',
    };
  }
}
