import { Users } from 'src/infrastructure/database/models/users.entity';
import { IRule } from './IRule';

const MINIMAL_TRANSACTIONS_TO_UPGRADE = 1;

export class ValidationCallAndOneTransactionPaidRule implements IRule {
  isSatisfied(user: Users) {
    const paidTransactionsInTime = user.cornerStore.getPaidTransactionsInTime(user);

    return {
      isSatisfied: Boolean(
        user.userDetail &&
          user.userDetail.activationCallCompletionDate &&
          paidTransactionsInTime.length >= MINIMAL_TRANSACTIONS_TO_UPGRADE,
      ),
      reason: 'Validation call and 1 transaction paid in time',
    };
  }
}
