import { Users } from 'src/infrastructure/database/models/users.entity';
import { IRule } from './IRule';

const MINIMAL_TRANSACTIONS_TO_UPGRADE = 1;

export class FormOneTransactionPaidRule implements IRule {
  isSatisfied(user: Users) {
    const paidTransactionsInTime = user.cornerStore.getPaidTransactionsInTime(user);

    return {
      isSatisfied:
        user.userDetail &&
        user.userDetail.kycFormCompletionDate &&
        paidTransactionsInTime.length >= MINIMAL_TRANSACTIONS_TO_UPGRADE,
      reason: 'KYC form completed and one transaction paid',
    };
  }
}
