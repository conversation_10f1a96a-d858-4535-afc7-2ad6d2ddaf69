import { Users } from 'src/infrastructure/database/models/users.entity';
import { IRule } from './IRule';

const MINIMAL_TRANSACTIONS_TO_UPGRADE = 3;

export class UserPaid3TransactionsRule implements IRule {
  isSatisfied(user: Users) {
    const paidTransactionsInTime = user.cornerStore.getPaidTransactionsInTime(user);
    return {
      isSatisfied: paidTransactionsInTime.length >= MINIMAL_TRANSACTIONS_TO_UPGRADE,
      reason: '3 transactions paid in time',
    };
  }
}
