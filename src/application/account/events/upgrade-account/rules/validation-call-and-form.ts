import { Users } from 'src/infrastructure/database/models/users.entity';
import { IRule } from './IRule';

export class ValidationCallAndFormRule implements IRule {
  isSatisfied(user: Users) {
    return {
      isSatisfied: <PERSON><PERSON>an(
        user.userDetail &&
          user.userDetail.activationCallCompletionDate &&
          user.userDetail.kycFormCompletionDate,
      ),
      reason: 'Validation call and KYC form completed',
    };
  }
}
