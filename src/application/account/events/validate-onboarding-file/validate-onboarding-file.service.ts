import { Injectable, Logger } from '@nestjs/common';

import { TrullyService } from 'src/infrastructure/external-services/trully/trully.service';
import { OnboardingDocument } from '../../dtos/update-verification-onboarding';
import { UserVerificationCode } from 'src/infrastructure/database/models/user-verification-code.entity';
import { OnboardingSteps } from 'src/application/constants';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { SaveKycValidationResultService } from 'src/middlewares/user-validator/use-cases/save-kyc-validation-result/save-kyc-validation-result.service';

@Injectable()
export class ValidateOnboardingFileService {
  logger = new Logger(ValidateOnboardingFileService.name);
  constructor(
    private readonly trullyService: TrullyService,
    private readonly userRepository: UserRepository,
    private readonly saveKycValidationResultService: SaveKycValidationResultService,
  ) {}

  private getPayload({
    base64Content,
    step,
    userId,
    onboardingRequestId,
  }: {
    base64Content: string;
    step: string;
    userId: string;
    onboardingRequestId: string;
  }) {
    const basePayload = {
      requestId: onboardingRequestId,
      userId,
    };

    switch (step) {
      case OnboardingSteps.DOCUMENT_IMAGE_FRONTAL:
        return {
          ...basePayload,
          documentImageFront: base64Content,
        };
      case OnboardingSteps.DOCUMENT_IMAGE_BACK:
        return {
          ...basePayload,
          documentImageBack: base64Content,
        };
      case OnboardingSteps.SELFIE_IMAGE:
        return {
          ...basePayload,
          selfieImage: base64Content,
        };
      default:
        return null;
    }
  }

  async handleValidateOnboardingFileEvent({
    document,
    step,
    userId,
    verification,
  }: {
    document: OnboardingDocument;
    step: string;
    userId: string;
    verification: UserVerificationCode;
  }) {
    if (step === OnboardingSteps.STORE_IMAGE) {
      return;
    }

    const payload = this.getPayload({
      base64Content: document.base64Content,
      step,
      userId,
      onboardingRequestId: verification.onboardingRequestId,
    });

    if (!payload) {
      return this.logger.error('Invalid onboarding step');
    }

    const trullyResponse = await this.trullyService.predict(payload);

    await this.saveKycValidationResultService.handler({
      userId,
      verificationId: verification.id,
      dataRaw: trullyResponse,
      requestId: trullyResponse.data.request_id,
    });

    if (!verification.onboardingRequestId) {
      await this.userRepository.updateVerificationOnboardingRequestId(
        verification.id,
        trullyResponse.data.request_id,
      );
    }

    this.logger.log(`Onboarding file ${step} validated for user ${userId}`);
  }
}
