import { Test, TestingModule } from '@nestjs/testing';
import { UserMergedServiceEvent } from './user-merged.service';

describe('UserMergedService', () => {
  let service: UserMergedServiceEvent;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UserMergedServiceEvent],
    }).compile();

    service = module.get<UserMergedServiceEvent>(UserMergedServiceEvent);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
