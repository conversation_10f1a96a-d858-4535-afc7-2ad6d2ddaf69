import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { EMPTY_SPACE, Environments, UserStatus } from 'src/application/constants';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { SlackService } from 'src/infrastructure/external-services/slack/slack.service';
import { WhatsappService } from 'src/infrastructure/external-services/whatsapp/whatsapp.service';

@Injectable()
export class UserMergedServiceEvent {
  private logger = new Logger(UserMergedServiceEvent.name);
  constructor(
    private readonly whatsappService: WhatsappService,
    private readonly slackService: SlackService,
  ) {}

  @OnEvent('account.merged', { async: false })
  async handleAccountMergedEvent({ mergedUser }: { mergedUser: Users }) {
    if (mergedUser.status.name !== UserStatus.VALIDATED) {
      const whatsappResponse = await this.whatsappService.flowExecution(mergedUser.phoneNumber, {
        flowsId: WhatsappService.WhatsappFlows.MERGED_USER_IS_BLOCKED,
        parameters: {},
      });

      this.logger.log(
        `Send message to user with phone number ${mergedUser.phoneNumber} with messageId ${whatsappResponse.data.id} `,
      );
    }

    const { wholesaler: currentWholesaler } = mergedUser.wholesalerInformations.find(
      (wholesalerInformation) => !wholesalerInformation.isMerged,
    );

    const { wholesaler: newWholesaler } = mergedUser.wholesalerInformations.find(
      (wholesalerInformation) => wholesalerInformation.isMerged,
    );

    if (process.env.NODE_ENV === Environments.PRODUCTION) {
      const blockedMessage =
        mergedUser.status.name !== UserStatus.VALIDATED
          ? '(Cliente no disponible para realizar compra)\n'
          : EMPTY_SPACE;

      await this.slackService.sendMessage(
        ':people_holding_hands: *Nuevo cliente fusionado* :people_holding_hands: \n' +
          `*Nombre:* ${mergedUser.firstName} ${mergedUser.lastNames || EMPTY_SPACE}\n` +
          `*Teléfono:* ${mergedUser.phoneNumber}\n` +
          `*Distribuidor original:* ${currentWholesaler.name}\n` +
          `*Distribuidor nuevo:* ${newWholesaler.name}\n` +
          blockedMessage +
          `<https://backoffice.propaga.mx/corner-store/${mergedUser.cornerStore.id}/v2|Ver en backoffice>`,
      );
    }
  }
}
