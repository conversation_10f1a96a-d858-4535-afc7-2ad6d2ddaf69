import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { EMPTY_SPACE } from 'src/application/constants';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { AlgoliaService } from 'src/infrastructure/external-services/algolia/algolia.service';
import { StatusTranslator } from 'src/utils/status-translator';

type UpdateSearchDTO = {
  user: Users;
  flags: {
    isUserInDefault?: boolean;
    isUserInPaymentPlan?: boolean;
    isActive?: boolean;
    isNew?: boolean;
    isBlocked?: boolean;
    signUpStatus?: string;
  };
};

@Injectable()
export class UpdateSearchUserService {
  logger = new Logger(UpdateSearchUserService.name);
  constructor(private readonly algoliaService: AlgoliaService) {}

  @OnEvent('user.updateSearch', { async: false })
  async handleUserUpdateSearchEvent({ user, flags }: UpdateSearchDTO) {
    const userIndexSearch = `user_${process.env.NODE_ENV}`;

    if (flags.signUpStatus) {
      flags.signUpStatus = StatusTranslator.translateSignUpStatus(flags.signUpStatus);
    }

    await this.algoliaService.addRecordToIndex(userIndexSearch, {
      ...user,
      fullName: `${user.firstName} ${user.lastNames || EMPTY_SPACE}`,
      status: {
        ...user.status,
        translatedStatusName: StatusTranslator.translateUserStatus(user.status.name),
      },
      ...flags,
      objectID: user.cornerStore.id,
    });

    this.logger.log(`User ${user.id} updated in search`);
  }
}
