import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { CornerStoreRepository } from 'src/infrastructure/database/repositories/corner-store.repository';
import { GoogleMapsService } from 'src/infrastructure/external-services/google-maps/google-maps.service';

@Injectable()
export class SaveAddressService {
  logger = new Logger(SaveAddressService.name);
  constructor(
    private readonly googleMapsService: GoogleMapsService,
    private readonly cornerStoreRepository: CornerStoreRepository,
  ) {}

  @OnEvent('user.save.address', { async: false })
  async handleSaveAddressEvent({
    user,
    latitude,
    longitude,
  }: {
    user: Users;
    latitude: string;
    longitude: string;
  }) {
    const addressResponse = await this.googleMapsService.getInverseGeocode(latitude, longitude);

    if (!addressResponse) {
      this.logger.error(`Address not found for latitude ${latitude} and longitude ${longitude}`);
      return;
    }

    await this.cornerStoreRepository.updateAddress(
      user.cornerStore.id,
      addressResponse.address,
      addressResponse.city,
      addressResponse.postalCode,
    );

    this.logger.log(`Address saved for user ${user.id}`);
  }
}
