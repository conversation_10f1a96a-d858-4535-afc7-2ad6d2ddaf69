import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { BucketNames } from 'src/infrastructure/constants';
import { UserVerificationCode } from 'src/infrastructure/database/models/user-verification-code.entity';
import { OnboardingFilesRepository } from 'src/infrastructure/database/repositories/onboarding-files.repository';
import { FileManagerService } from 'src/infrastructure/file-manager/file-manager.service';
import { OnboardingDocument } from '../../dtos/update-verification-onboarding';
import { Environments } from 'src/application/constants';
import { randomUUID } from 'crypto';

@Injectable()
export class UploadOnboardingFileService {
  private logger = new Logger(UploadOnboardingFileService.name);
  constructor(
    private readonly fileManagerService: FileManagerService,
    private readonly onboardingFilesRepository: OnboardingFilesRepository,
  ) {}

  @OnEvent('user.upload.onboarding.document', { async: false })
  async handleUserUploadOnboardingFileEvent({
    document,
    step,
    userId,
    verification,
  }: {
    document: OnboardingDocument;
    step: string;
    userId: string;
    verification: UserVerificationCode;
  }) {
    if (process.env.NODE_ENV === Environments.TESTING) {
      return;
    }

    const imageBuffer = Buffer.from(document.base64Content, 'base64');

    const fileName = `${userId}/${verification.id}_${step.toLowerCase()}_${randomUUID()}.${
      document.fileExtension
    }`;

    const { Location } = await this.fileManagerService.uploadFile({
      buffer: imageBuffer,
      bucketName: BucketNames.ONBOARDING_FILES,
      fileName,
      contentType: `application/${document.fileExtension}`,
    });

    const existingFile = await this.onboardingFilesRepository.findByVerificationCodeIdAndType({
      verificationCodeId: verification.id,
      type: step,
    });

    if (existingFile) {
      await this.onboardingFilesRepository.updateFileUrl({
        verificationCodeId: verification.id,
        type: step,
        fileUrl: Location,
      });

      return this.logger.log(
        `Document ${step} updated for user ${userId} with verification ${verification.id}`,
      );
    }

    await this.onboardingFilesRepository.create({
      userVerificationCode: verification,
      type: step,
      fileUrl: Location,
    });

    this.logger.log(
      `Document ${step} uploaded for user ${userId} with verification ${verification.id}`,
    );
  }
}
