import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import {
  FeatureFlags,
  UserBlockingSources,
  UserRiskFraudScores,
  UserStatus,
} from 'src/application/constants';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { UserValidationResultRepository } from 'src/infrastructure/database/repositories/user-validation-result.repository';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { WholesalerRepository } from 'src/infrastructure/database/repositories/wholesaler.repository';
import { FeatureFlagService } from 'src/infrastructure/feature-flag/feature-flag.service';
import { WholesalerStrategyService } from 'src/infrastructure/wholesaler/wholesaler-strategy.service';
import { UserFraudRiskResponse } from 'src/middlewares/user-validator/interfaces/user-fraud-risk-result';
import { ValidateUserPayload } from 'src/middlewares/user-validator/interfaces/user-validator';
import { UserValidatorService } from 'src/middlewares/user-validator/user-validator.service';

@Injectable()
export class CallValidatorService {
  private logger = new Logger(CallValidatorService.name);
  constructor(
    private readonly userValidatorService: UserValidatorService,
    private readonly userValidationResultRepository: UserValidationResultRepository,
    private readonly wholesalerRepository: WholesalerRepository,
    private readonly userRepository: UserRepository,
    private readonly wholesalerStrategyService: WholesalerStrategyService,
    private readonly featureFlagService: FeatureFlagService,
  ) {}

  private async getIsActiveUserActivationNotification(wholesalerName: string) {
    return this.featureFlagService.getFeatureFlagWithWholesaler(
      FeatureFlags.IS_ACTIVE_USER_ACTIVATION_NOTIFICATION,
      wholesalerName,
    );
  }

  private async evaluateUserValidation(
    validatePayload: any,
    user: Users,
    verificationId?: string,
    isManualExecution = false,
  ) {
    const userPayload = {
      ...validatePayload,
    } as ValidateUserPayload;

    const validationResult = await this.userValidationResultRepository.create({
      isValidated: false,
      user: user,
      verificationId,
      statusName: 'issued',
      id: userPayload.validatorId,
    });

    const userFraudRiskResponse = await this.userValidatorService.getUserFraudRiskScore(
      userPayload,
    );

    if (
      !userFraudRiskResponse ||
      !userFraudRiskResponse.validations ||
      userFraudRiskResponse.validations.includes(null)
    ) {
      this.logger.error('Error on call validator');
      this.logger.error(userFraudRiskResponse);
    }

    const fraudChecks =
      userFraudRiskResponse && userFraudRiskResponse.validations
        ? userFraudRiskResponse.validations
            .filter((validation) => validation)
            .map(({ context }) => context)
            .flat()
        : [];

    const isValidated = userFraudRiskResponse.fraudRiskScore === UserRiskFraudScores.OK_VALUE;

    await this.userValidationResultRepository.update(validationResult.id, {
      validationChecks: userFraudRiskResponse.validations,
      fraudChecks: {
        fraudRiskScore: userFraudRiskResponse.fraudRiskScore,
        validations: fraudChecks,
      },
      isValidated,
    });

    if (!isManualExecution) {
      return userFraudRiskResponse;
    }

    await this.handleManualExecutionResult(isValidated, user, userFraudRiskResponse);
    return userFraudRiskResponse;
  }

  private async handleManualExecutionResult(
    isValidated: boolean,
    user: Users,
    userFraudRiskResponse: UserFraudRiskResponse,
  ) {
    if (isValidated && user.isBlocked()) {
      await this.wholesalerRepository.setBlockedStatusByUserId({
        userId: user.id,
        isBlocked: false,
      });
    }

    if (isValidated && user.status.name === UserStatus.MANUAL_VALIDATION) {
      await this.userRepository.updateUserStatus(user.id, UserStatus.VALIDATED);

      for (const wholesalerInformation of user.wholesalerInformations) {
        const { wholesaler } = wholesalerInformation;

        const isActiveUserActivationNotification = await this.getIsActiveUserActivationNotification(
          wholesaler.name,
        );

        if (isActiveUserActivationNotification) {
          await this.wholesalerStrategyService.sendUserActivationConfirmation(
            user,
            wholesaler.name,
          );
        }
      }
    }

    if (!isValidated && userFraudRiskResponse.fraudRiskScore === UserRiskFraudScores.MAX_VALUE) {
      await this.wholesalerRepository.setBlockedStatusByUserId({
        userId: user.id,
        isBlocked: true,
      });

      await this.userRepository.updateUserStatus(user.id, UserStatus.MANUAL_VALIDATION);

      for (const wholesalerInformation of user.wholesalerInformations) {
        await this.wholesalerRepository.createUserIsBlockedHistory({
          wholesalerInformation,
          source: UserBlockingSources.USER_VALIDATOR,
        });
      }
    }
  }

  @OnEvent('validator.call', { async: false })
  async handleCallValidatorEvent({
    validatePayload,
    user,
    verificationId,
    isManualExecution,
  }: {
    validatePayload: any;
    user: Users;
    verificationId?: string;
    isManualExecution?: boolean;
  }) {
    this.logger.log(`Call validator event to user ${user.id}`);
    await this.evaluateUserValidation(validatePayload, user, verificationId, isManualExecution);
    this.logger.log(`Call validator event to user ${user.id} finished`);
  }
}
