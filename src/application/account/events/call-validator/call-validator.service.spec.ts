import { Test, TestingModule } from '@nestjs/testing';
import { CallValidatorService } from './call-validator.service';

describe('CallValidatorService', () => {
  let service: CallValidatorService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CallValidatorService],
    }).compile();

    service = module.get<CallValidatorService>(CallValidatorService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
