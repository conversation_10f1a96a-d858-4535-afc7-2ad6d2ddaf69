import { Type } from 'class-transformer';
import {
  IsBase64,
  IsE<PERSON>,
  IsNotEmpty,
  IsObject,
  IsString,
  Valida<PERSON>If,
  ValidateNested,
} from 'class-validator';

export enum OnboardingSteps {
  documentImageFrontal = 'DOCUMENT_IMAGE_FRONTAL',
  documentImageBack = 'DOCUMENT_IMAGE_BACK',
  selfieImage = 'SELFIE_IMAGE',
  storeImage = 'STORE_IMAGE',
  cornerStoreInformation = 'CORNER_STORE_INFORMATION',
  completed = 'COMPLETED',
  addressRequest = 'ADDRESS_REQUEST',
}

export enum AcceptedTypes {
  png = 'png',
  jpg = 'jpg',
  jpeg = 'jpeg',
}

export class OnboardingDocument {
  @IsString()
  @IsNotEmpty()
  @IsEnum(AcceptedTypes, { message: 'You should send a valid file extension' })
  public fileExtension: AcceptedTypes;

  @IsBase64()
  @IsNotEmpty()
  public base64Content: string;
}

export enum CornerStoreTypes {
  ABARROTES = 'ABARROTES',
  CAFE_INTERNET = 'CAFE_INTERNET',
  CARNICERIA = 'CARNICERIA',
  CREMERIA = 'CREMERIA',
  DULCERIA = 'DULCERIA',
  FARMACIA = 'FARMACIA',
  FERRETERIA = 'FERRETERIA',
  MISCELANEA = 'MISCELANEA',
  PAPELERIA = 'PAPELERIA',
  RESTAURANTE = 'RESTAURANTE',
  POLLERIA = 'POLLERIA',
}

export enum BusinnessLifetimeTypes {
  LESS_THAN_SIX_MONTHS = 'LESS_THAN_SIX_MONTHS',
  LESS_THAN_ONE_YEAR = 'LESS_THAN_ONE_YEAR',
  ONE_TO_TWO_YEARS = 'ONE_TO_TWO_YEARS',
  TWO_TO_THREE_YEARS = 'TWO_TO_THREE_YEARS',
  THREE_TO_FIVE_YEARS = 'THREE_TO_FIVE_YEARS',
  MORE_THAN_FIVE_YEARS = 'MORE_THAN_FIVE_YEARS',
}

export enum OwnershipStatusTypes {
  DUENO = 'DUEÑO',
  EMPLEADO = 'EMPLEADO',
}

export enum LocaleOwner {
  PROPIO = 'PROPIO',
  ALQUILADO = 'ALQUILADO',
}
export class OnboardingInformation {
  @ValidateIf((o) => !o.cornerStoreAddress)
  @IsString()
  @IsNotEmpty()
  @IsEnum(CornerStoreTypes, { message: 'You should send a valid corner store type' })
  public cornerStoreType?: string;

  @ValidateIf((o) => !o.cornerStoreAddress)
  @IsString()
  @IsNotEmpty()
  @IsEnum(BusinnessLifetimeTypes, { message: 'You should send a valid business lifetime type' })
  public businessLifetime?: string;

  @ValidateIf((o) => !o.cornerStoreAddress)
  @IsString()
  @IsNotEmpty()
  @IsEnum(OwnershipStatusTypes, { message: 'You should send a valid ownership status type' })
  public ownershipStatus?: string;

  @ValidateIf((o) => !o.cornerStoreAddress)
  @IsString()
  @IsNotEmpty()
  public cornerStoreName?: string;

  @IsString()
  @IsNotEmpty()
  public cornerStoreAddress: string;

  @ValidateIf((o) => !o.cornerStoreAddress)
  @IsString()
  @IsNotEmpty()
  @IsEnum(LocaleOwner, { message: 'You should send a valid locale owner type' })
  public localeOwner?: string;
}

export class UpdateVerificationOnboardingDTO {
  @IsString()
  @IsNotEmpty()
  @IsEnum(OnboardingSteps, { message: 'You should send a valid onboarding step' })
  public step: OnboardingSteps;

  @ValidateIf((object) => !object.information && object.step !== OnboardingSteps.completed)
  @IsObject()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => OnboardingDocument)
  public document?: OnboardingDocument;

  @ValidateIf((object) => !object.document && object.step !== OnboardingSteps.completed)
  @IsObject()
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => OnboardingInformation)
  public information?: OnboardingInformation;

  @ValidateIf(
    (object) => object.document && object.information && object.step !== OnboardingSteps.completed,
  )
  @IsNotEmpty({ message: 'Only one of document or information should be provided' })
  private readonly _: undefined;
}
