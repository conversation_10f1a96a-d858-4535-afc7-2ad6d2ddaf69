import {
  Is<PERSON>rray,
  IsE<PERSON>,
  IsLatitude,
  IsLongitude,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { IsPhoneNumberValid } from './phone-validator';

export class Contact {
  @IsNotEmpty()
  @IsString()
  fullName: string;

  @IsNotEmpty()
  @IsPhoneNumberValid()
  phoneNumber: string;

  @IsOptional()
  @IsString()
  type?: string;
}

export class CreateVerificationDTO {
  @IsString()
  @IsNotEmpty()
  @IsPhoneNumberValid()
  public phoneNumber: string;

  @IsOptional()
  @IsLatitude()
  public latitude?: string | undefined;

  @IsOptional()
  @IsLongitude()
  public longitude?: string | undefined;

  @IsNotEmpty()
  public firstName: string;

  @IsNotEmpty()
  public lastNames: string;

  @IsOptional()
  @IsEmail()
  public email: string;

  @IsOptional()
  birthdate?: string;

  @IsNotEmpty()
  @IsArray()
  @Type(() => Contact)
  @ValidateNested({ each: true })
  public contacts: Contact[];

  @IsOptional()
  @IsString()
  public contactSelectionType: string;

  @IsOptional()
  @IsObject()
  public metadata: object;
}

export class CreateVerificationUseCaseDTO {
  phoneNumber: string;
  userId: string;
  wholesalerName: string;
  latitude?: string;
  longitude?: string;
  firstName?: string;
  lastNames?: string;
  contacts?: Contact[];
  email?: string;
  birthdate?: string;
  contactSelectionType?: string;
  metadata?: object;
}
