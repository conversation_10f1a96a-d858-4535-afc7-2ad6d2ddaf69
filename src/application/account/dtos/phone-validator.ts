import { registerDecorator, ValidationOptions, isPhoneNumber } from 'class-validator';
import { Environments } from 'src/application/constants';

export function IsPhoneNumberValid(validationOptions?: ValidationOptions) {
  return function (object: unknown, propertyName: string) {
    registerDecorator({
      name: 'IsPhoneNumberValid',
      target: object.constructor,
      propertyName: propertyName,
      options: {
        ...validationOptions,
        message: `${propertyName} must be a valid phone number`,
      },
      validator: {
        validate(value: any) {
          if (process.env.NODE_ENV !== Environments.PRODUCTION) {
            return true;
          }

          return isPhoneNumber(value, 'MX');
        },
      },
    });
  };
}
