import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UserRepository } from 'src/infrastructure/database/repositories/user.repository';
import { CornerStoreRepository } from 'src/infrastructure/database/repositories/corner-store.repository';
import { TrullyService } from 'src/infrastructure/external-services/trully/trully.service';
import { JobNames, QueueNames } from 'src/infrastructure/constants';
import { Job } from 'bullmq';
import { OnboardingSteps } from 'src/application/constants';
import { Process } from '@nestjs/bull';
import { ValidateOnboardingFileService } from '../events/validate-onboarding-file/validate-onboarding-file.service';

@Processor(QueueNames.USER)
export class OnboardingProcessor extends WorkerHost {
  private readonly logger = new Logger(OnboardingProcessor.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly cornerStoreRepository: CornerStoreRepository,
    private readonly eventEmitter: EventEmitter2,
    private readonly trullyService: TrullyService,
    private readonly validateOnboardingFileService: ValidateOnboardingFileService,
  ) {
    super();
  }

  private async saveVerificationData(verification, information) {
    await this.cornerStoreRepository.updateAddressAndCornerStoreName(
      verification.user.cornerStore.id,
      information.cornerStoreAddress,
      information.cornerStoreName,
    );

    await this.cornerStoreRepository.updateCornerStoreDetail(verification.user.cornerStore.id, {
      type: information.cornerStoreType,
      businessLifetime: information.businessLifetime,
      localeOwner: information.localeOwner,
      ownershipStatus: information.ownershipStatus,
    });
  }

  @Process(JobNames.ONBOARDING)
  async process(job: Job) {
    const { verificationId, userId, document, information, step } = job.data;
    this.logger.log(`Processing onboarding job ${job.name} for step ${step}`);

    const verification = await this.userRepository.getVerificationbyId(verificationId);
    if (!verification) {
      throw new Error('Verification not found');
    }

    switch (step) {
      case OnboardingSteps.CORNER_STORE_INFORMATION:
        await this.saveVerificationData(verification, information);
        break;

      case OnboardingSteps.ADDRESS_REQUEST:
        const trullyResponse = await this.trullyService.predict({
          address: information.cornerStoreAddress,
          userId: userId,
        });

        await this.userRepository.updateVerificationOnboardingRequestId(
          verification.id,
          trullyResponse.data.request_id,
        );
        break;

      default:
        await this.validateOnboardingFileService.handleValidateOnboardingFileEvent({
          document,
          step,
          userId,
          verification,
        });

        this.eventEmitter.emit('user.upload.onboarding.document', {
          document,
          step,
          userId,
          verification,
        });
        break;
    }

    this.logger.log(`Completed processing onboarding job ${job.name} for step ${step}`);
  }
}
