import { <PERSON>, HttpCode, HttpStatus, Logger, Post, UseGuards } from '@nestjs/common';
import { ChangeTransactionStatusService } from './use-cases/change-transaction-status/change-transaction-status.service';
import { CreateWholesalerTransferService } from './use-cases/create-wholesaler-transfer/create-wholesaler-transfer.service';
import { Environments, WholesalerNames } from '../constants';
import { CancelPendingTransactionsService } from './use-cases/cancel-pending-transactions/cancel-pending-transactions.service';
import { CreatePaymentPlanTicketService } from './use-cases/create-payment-plan-ticket/create-payment-plan-ticket.service';
import { CancelGhostTransactionsService } from './use-cases/cancel-ghost-transactions/cancel-ghost-transactions.service';
import { CreateAutoReceiptService } from './use-cases/create-auto-receipt/create-auto-receipt.service';
import { InternalTokenGuard } from '../../authentication/internal-token.guard';

@Controller('cron-transaction')
export class CronTransactionController {
  logger = new Logger(CronTransactionController.name);

  constructor(
    private readonly changeTransactionStatusService: ChangeTransactionStatusService,
    private readonly createWholesalerTransferService: CreateWholesalerTransferService,
    private readonly cancelPendingTransactionService: CancelPendingTransactionsService,
    private readonly createPaymentPlanTicketService: CreatePaymentPlanTicketService,
    private readonly cancelGhostTransactionsService: CancelGhostTransactionsService,
    private readonly createAutoReceiptService: CreateAutoReceiptService,
  ) {}

  // Schedule: EVERY_DAY_AT_1AM (0 1 * * *)
  @Post('/transaction-status-update')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  handleTransactionStatusUpdate() {
    return this.changeTransactionStatusService.handler();
  }

  // Schedule: MONDAY_TO_SATURDAY_AT_8AM (0 0 08 * * 1-6)
  @Post('/wholesaler-transfer/rabbit')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  handleWholesalerTransferForRabbit() {
    return this.createWholesalerTransferService.handler(WholesalerNames.RABBIT);
  }

  // Schedule: MONDAY_TO_SATURDAY_AT_7AM (0 0 07 * * 1-6)
  @Post('/wholesaler-transfer/chiper')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  handleWholesalerTransferForChiper() {
    return this.createWholesalerTransferService.handler(WholesalerNames.CHIPER);
  }

  // Schedule: MONDAY_TO_SATURDAY_AT_6AM (0 0 06 * * 1-6)
  @Post('/wholesaler-transfer/rintin')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  handleWholesalerTransferForRintin() {
    return this.createWholesalerTransferService.handler(WholesalerNames.RINTIN);
  }

  // Schedule: MONDAY_TO_SATURDAY_AT_6AM (0 0 06 * * 1-6)
  @Post('/wholesaler-transfer/nestle')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  handleWholesalerTransferForNestle() {
    return this.createWholesalerTransferService.handler(WholesalerNames.NESTLE);
  }

  // Schedule: MONDAY_TO_SATURDAY_AT_6AM (0 0 06 * * 1-6)
  @Post('/wholesaler-transfer/mercanto')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  handleWholesalerTransferForMercanto() {
    return this.createWholesalerTransferService.handler(WholesalerNames.MERCANTO);
  }

  // Schedule: MONDAY_TO_SATURDAY_AT_7AM (0 0 07 * * 1-6)
  @Post('/wholesaler-transfer/surtace')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  handleWholesalerTransferForSurtace() {
    return this.createWholesalerTransferService.handler(WholesalerNames.SURTACE);
  }

  // Schedule: MONDAY_TO_SATURDAY_AT_6AM (0 0 06 * * 1-6)
  @Post('/wholesaler-transfer/scorpion')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  handleWholesalerTransferForScorpion() {
    return this.createWholesalerTransferService.handler(WholesalerNames.SCORPION);
  }

  // Schedule: MONDAY_TO_SATURDAY_AT_6AM (0 0 06 * * 1-6)
  @Post('/wholesaler-transfer/helados-holanda')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  handleWholesalerTransferForHeladosHolanda() {
    return this.createWholesalerTransferService.handler(WholesalerNames.HELADOS_HOLANDA);
  }

  // Schedule: EVERY_30_MINUTES (*/30 * * * *)
  @Post('/cancel-pending-transactions')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  handleCancelPendingTransactions() {
    return this.cancelPendingTransactionService.handler();
  }

  // Schedule: EVERY_15_MINUTES (0 */15 * * * *)
  @Post('/create-payment-plan-ticket')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  handleCreatePaymentPlanTicket() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run cronCreatePaymentPlanTicketService, but you are in ${process.env.NODE_ENV} env`,
      );
    }

    return this.createPaymentPlanTicketService.handler();
  }

  // Schedule: EVERY_DAY_AT_7PM (0 19 * * *)
  @Post('/cancel-ghost-transactions')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  handleCancelGhostTransactions() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run cronCancelGhostTransactions, but you are in ${process.env.NODE_ENV} env`,
      );
    }
    this.logger.log('Starting cancel ghost transactions job');
    return this.cancelGhostTransactionsService.handler();
  }

  // Schedule: EVERY_DAY_AT_MIDNIGHT (0 0 * * *)
  @Post('/create-automatic-receipts')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  handleCreateAutomaticReceipts() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run cronCreateAutoReceiptService, but you are in ${process.env.NODE_ENV} env`,
      );
    }

    this.logger.log('Starting create automatic receipts job');
    return this.createAutoReceiptService.handler();
  }
}
