import { Injectable } from '@nestjs/common';
import { BackofficeCommands } from 'src/application/backoffice/constants';
import {
  CommandStrategyDependencies,
  CommandStrategyResponse,
  StrategyIncomeCommand,
} from 'src/application/backoffice/interfaces/command.interface';
import { CreateCollectionRouteService } from 'src/application/collection/use-cases/create-collection-route/create-collection-route.service';
import { BackofficeHistoryRepository } from 'src/infrastructure/database/repositories/backoffice-history.repository';

@Injectable()
export class CreateCollectionRouteStrategy implements StrategyIncomeCommand {
  private readonly createCollectionRouteService: CreateCollectionRouteService;
  private readonly backofficeHistoryRepository: BackofficeHistoryRepository;

  constructor({
    createCollectionRouteService,
    backofficeHistoryRepository,
  }: CommandStrategyDependencies) {
    this.createCollectionRouteService = createCollectionRouteService;
    this.backofficeHistoryRepository = backofficeHistoryRepository;
  }

  async handler(args: any): Promise<any> {
    const modifiedBy = args['modifiedBy'];
    const agentId = args['agentId'];
    const userIds = args['userIds'];

    const response = await this.createCollectionRouteService.handler({
      agentId,
      userIds,
    });

    await this.backofficeHistoryRepository.create({
      action: BackofficeCommands.CREATE_COLLECTION_ROUTE,
      modifiedBy,
      entities: { userIds: args['userIds'] },
    });

    return {
      response,
    } as CommandStrategyResponse;
  }
}
