import { Controller, HttpCode, HttpStatus, Post, UseGuards } from '@nestjs/common';
import { CreateRevenueShareTransferService } from './use-cases/create-revenue-share-transfer/create-revenue-share-transfer.service';
import { WholesalerNames } from '../constants';
import { InternalTokenGuard } from '../../authentication/internal-token.guard';

@Controller('cron-revenue-share')
export class CronRevenueShareController {
  constructor(
    private readonly createRevenueShareTransferService: CreateRevenueShareTransferService,
  ) {}

  // Schedule: EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT (0 0 1 * *)
  @Post('/revenue-share-transfer/rabbit')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  createRevenueShareTransferForRabbit() {
    return this.createRevenueShareTransferService.handler(WholesalerNames.RABBIT);
  }

  // Schedule: EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT (0 0 1 * *)
  @Post('/revenue-share-transfer/chiper')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  createRevenueShareTransferForChiper() {
    return this.createRevenueShareTransferService.handler(WholesalerNames.CHIPER);
  }
}
