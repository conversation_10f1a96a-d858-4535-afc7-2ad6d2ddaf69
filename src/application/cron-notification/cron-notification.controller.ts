import { <PERSON>, <PERSON>ttp<PERSON><PERSON>, Http<PERSON>tatus, <PERSON><PERSON>, Post, UseGuards } from '@nestjs/common';
import { UnusedCreditNotificationService } from './use-cases/unused-credit-notification/unused-credit-notification.service';
import { PaymentDayNotificationService } from './use-cases/payment-day-notification/payment-day-notification.service';
import { Environments, WholesalerNames } from '../constants';
import { InDefaultUserNotificationService } from './use-cases/in-default-user-notification/in-default-user-notification.service';
import { PaymentExpiresSoonNotificationService } from './use-cases/payment-expires-soon-notification/payment-expires-soon-notification.service';
import { PaymentPlanTicketNotificationService } from './use-cases/payment-plan-ticket-notification/payment-plan-ticket-notification.service';
import { CreditLimitUpdateNotificationService } from './use-cases/credit-limit-update-notification/credit-limit-update-notification.service';
import { DeliveryWordNotificationService } from './use-cases/delivery-word-notification/delivery-word-notification.service';
import { WhatsappService } from 'src/infrastructure/external-services/whatsapp/whatsapp.service';
import { InternalTokenGuard } from '../../authentication/internal-token.guard';

@Controller('cron-notification')
export class CronNotificationController {
  logger = new Logger(CronNotificationController.name);
  constructor(
    private readonly unusedCreditNotificationService: UnusedCreditNotificationService,
    private readonly paymentDayNotificationService: PaymentDayNotificationService,
    private readonly inDefaultUserNotificationService: InDefaultUserNotificationService,
    private readonly paymentExpiresSoonNotificationService: PaymentExpiresSoonNotificationService,
    private readonly paymentPlanTicketNotificationService: PaymentPlanTicketNotificationService,
    private readonly creditLimitUpdateNotificationService: CreditLimitUpdateNotificationService,
    private readonly deliveryWordNotificationService: DeliveryWordNotificationService,
  ) {}

  // Schedule: Manual trigger (no cron schedule found)
  @Post('/unused-credit-notification')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  sendUnusedCreditNotification() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run unused-credit notification, but you are in ${process.env.NODE_ENV} env`,
      );
    }
    return this.unusedCreditNotificationService.handle();
  }

  // Schedule: EVERY_5_MINUTES (*/5 * * * *)
  @Post('/payment-day-notification')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  async sendPaymentDayNotification() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run payment-day notification, but you are in ${process.env.NODE_ENV} env`,
      );
    }
    return this.paymentDayNotificationService.handler(WhatsappService.WhatsappFlows.PAYMENT_DAY);
  }

  // Schedule: EVERY_DAY_AT_1PM (0 13 * * *)
  @Post('/second-payment-day-notification')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  sendSecondPaymentDayNotification() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run second payment-day notification, but you are in ${process.env.NODE_ENV} env`,
      );
    }
    return this.paymentDayNotificationService.handler(
      WhatsappService.WhatsappFlows.SECOND_PAYMENT_DAY_REMINDER,
    );
  }

  // Schedule: EVERY_DAY_AT_6PM (0 18 * * *)
  @Post('/third-payment-day-notification')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  sendThirdPaymentDayNotification() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run third payment-day notification, but you are in ${process.env.NODE_ENV} env`,
      );
    }
    return this.paymentDayNotificationService.handler(
      WhatsappService.WhatsappFlows.THIRD_PAYMENT_DAY_REMINDER,
    );
  }

  // Schedule: Manual trigger (no cron schedule found)
  @Post('/in-default-user-notification')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  sendInDefaultUserNotification() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run in-default-user notification, but you are in ${process.env.NODE_ENV} env`,
      );
    }
    return this.inDefaultUserNotificationService.handler();
  }

  // Schedule: EVERY_DAY_AT_11AM (0 11 * * *)
  @Post('/payment-expires-soon-notification')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  sendPaymentExpiresSoonNotification() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run payment-expires-soon notification, but you are in ${process.env.NODE_ENV} env`,
      );
    }
    return this.paymentExpiresSoonNotificationService.handler();
  }

  // Schedule: EVERY_DAY_AT_NOON (0 12 * * *)
  @Post('/payment-plan-ticket-notification')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  sendPaymentPlanTicketNotification() {
    return this.paymentPlanTicketNotificationService.handler();
  }

  // Schedule: MONDAY_TO_FRIDAY_AT_11AM (0 11 * * 1-5)
  @Post('/credit-limit-update-notification')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  sendCreditLimitUpdateNotification() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run credit-limit-update notification, but you are in ${process.env.NODE_ENV} env`,
      );
    }
    return this.creditLimitUpdateNotificationService.handler();
  }

  // Schedule: EVERY_DAY_AT_7_30_AM (30 07 * * *)
  @Post('/delivery-word-notification/rabbit')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  sendDeliveryWordNotificationForRabbit() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run delivery-word notification for rabbit, but you are in ${process.env.NODE_ENV} env`,
      );
    }

    return this.deliveryWordNotificationService.handler(WholesalerNames.RABBIT);
  }
}
