import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { UnusedCreditNotificationService } from './use-cases/unused-credit-notification/unused-credit-notification.service';
import { PaymentDayNotificationService } from './use-cases/payment-day-notification/payment-day-notification.service';
import { CronCustomExpressions, Environments, WholesalerNames } from '../constants';
import { InDefaultUserNotificationService } from './use-cases/in-default-user-notification/in-default-user-notification.service';
import { PaymentExpiresSoonNotificationService } from './use-cases/payment-expires-soon-notification/payment-expires-soon-notification.service';
import { PaymentPlanTicketNotificationService } from './use-cases/payment-plan-ticket-notification/payment-plan-ticket-notification.service';
import { CreditLimitUpdateNotificationService } from './use-cases/credit-limit-update-notification/credit-limit-update-notification.service';
import { DeliveryWordNotificationService } from './use-cases/delivery-word-notification/delivery-word-notification.service';
import { WhatsappService } from 'src/infrastructure/external-services/whatsapp/whatsapp.service';
import { RedlockService } from '../../infrastructure/redlock/redlock.service';
import { DistributedLock } from '../../infrastructure/redlock/distributed-lock.decorator';

@Injectable()
export class CronNotificationController {
  logger = new Logger(CronNotificationController.name);
  constructor(
    private readonly unusedCreditNotificationService: UnusedCreditNotificationService,
    private readonly paymentDayNotificationService: PaymentDayNotificationService,
    private readonly inDefaultUserNotificationService: InDefaultUserNotificationService,
    private readonly paymentExpiresSoonNotificationService: PaymentExpiresSoonNotificationService,
    private readonly paymentPlanTicketNotificationService: PaymentPlanTicketNotificationService,
    private readonly creditLimitUpdateNotificationService: CreditLimitUpdateNotificationService,
    private readonly deliveryWordNotificationService: DeliveryWordNotificationService,
    private readonly redlockService: RedlockService,
  ) {}

  sendUnusedCreditNotification() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run unused-credit notification, but you are in ${process.env.NODE_ENV} env`,
      );
    }
    return this.unusedCreditNotificationService.handle();
  }

  @Cron(CronExpression.EVERY_5_MINUTES, {
    name: 'cronNotificationPaymentDay',
    timeZone: 'America/Mexico_City',
  })
  async sendPaymentDayNotification() {
    return await this.redlockService.withLock(
      'notification:payment-day',
      300000, // 5 minutes TTL
      async () => {
        if (process.env.NODE_ENV !== Environments.PRODUCTION) {
          return this.logger.log(
            `It should run payment-day notification, but you are in ${process.env.NODE_ENV} env`,
          );
        }
        // Your critical section code here
        return this.paymentDayNotificationService.handler(
          WhatsappService.WhatsappFlows.PAYMENT_DAY,
        );
      },
    );
  }

  @DistributedLock({
    key: 'notification:second-payment-day',
    ttl: 600000, // 10 minutes
    skipOnLockFailure: true,
  })
  @Cron(CronExpression.EVERY_DAY_AT_1PM, {
    name: 'cronNotificationSecondPaymentDay',
    timeZone: 'America/Mexico_City',
  })
  sendSecondPaymentDayNotification() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run second payment-day notification, but you are in ${process.env.NODE_ENV} env`,
      );
    }
    return this.paymentDayNotificationService.handler(
      WhatsappService.WhatsappFlows.SECOND_PAYMENT_DAY_REMINDER,
    );
  }

  @DistributedLock({
    key: 'notification:third-payment-day',
    ttl: 600000, // 10 minutes
    skipOnLockFailure: true,
  })
  @Cron(CronExpression.EVERY_DAY_AT_6PM, {
    name: 'cronNotificationThirdPaymentDay',
    timeZone: 'America/Mexico_City',
  })
  sendThirdPaymentDayNotification() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run third payment-day notification, but you are in ${process.env.NODE_ENV} env`,
      );
    }
    return this.paymentDayNotificationService.handler(
      WhatsappService.WhatsappFlows.THIRD_PAYMENT_DAY_REMINDER,
    );
  }

  sendInDefaultUserNotification() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run in-default-user notification, but you are in ${process.env.NODE_ENV} env`,
      );
    }
    return this.inDefaultUserNotificationService.handler();
  }

  @DistributedLock({
    key: 'notification:payment-expires-soon',
    ttl: 600000, // 10 minutes
    skipOnLockFailure: true,
  })
  @Cron(CronExpression.EVERY_DAY_AT_11AM, {
    name: 'cronNotificationPaymentExpiresSoon',
    timeZone: 'America/Mexico_City',
  })
  sendPaymentExpiresSoonNotification() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run payment-expires-soon notification, but you are in ${process.env.NODE_ENV} env`,
      );
    }
    return this.paymentExpiresSoonNotificationService.handler();
  }

  @DistributedLock({
    key: 'notification:payment-plan-ticket',
    ttl: 600000, // 10 minutes
    skipOnLockFailure: true,
  })
  @Cron(CronExpression.EVERY_DAY_AT_NOON, {
    name: 'cronSendPaymentPlanTicketNotification',
    timeZone: 'America/Mexico_City',
  })
  sendPaymentPlanTicketNotification() {
    return this.paymentPlanTicketNotificationService.handler();
  }

  @DistributedLock({
    key: 'notification:credit-limit-update',
    ttl: 600000, // 10 minutes
    skipOnLockFailure: true,
  })
  @Cron(CronExpression.MONDAY_TO_FRIDAY_AT_11AM, {
    name: 'cronCreditLimitUpdateNotification',
    timeZone: 'America/Mexico_City',
  })
  sendCreditLimitUpdateNotification() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run credit-limit-update notification, but you are in ${process.env.NODE_ENV} env`,
      );
    }
    return this.creditLimitUpdateNotificationService.handler();
  }

  @DistributedLock({
    key: 'notification:delivery-word:rabbit',
    ttl: 600000, // 10 minutes
    skipOnLockFailure: true,
  })
  @Cron(CronCustomExpressions.EVERY_DAY_AT_7_30_AM, {
    name: 'cronDeliveryWordNotificationForRabbit',
    timeZone: 'America/Mexico_City',
  })
  sendDeliveryWordNotificationForRabbit() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return this.logger.log(
        `It should run delivery-word notification for rabbit, but you are in ${process.env.NODE_ENV} env`,
      );
    }

    return this.deliveryWordNotificationService.handler(WholesalerNames.RABBIT);
  }
}
