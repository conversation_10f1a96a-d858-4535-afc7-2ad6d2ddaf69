import { <PERSON>, HttpC<PERSON>, HttpStatus, Lo<PERSON>, Post, UseGuards } from '@nestjs/common';
import { Environments } from '../constants';
import { UpdateSearchClientService } from './use-cases/update-search-client/update-search-client.service';
import { InternalTokenGuard } from '../../authentication/internal-token.guard';

@Controller('cron-corner-store')
export class CronCornerStoreController {
  logger = new Logger(CronCornerStoreController.name);
  constructor(
    private readonly updateSearchClientService: UpdateSearchClientService,
  ) {}

  // Schedule: EVERY_25_MINUTES (0 */25 * * * *)
  @Post('/update-search-client-prod')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  updateSearchClientProd() {
    if (process.env.NODE_ENV !== Environments.PRODUCTION) {
      return;
    }

    return this.updateSearchClientService.handler();
  }

  // Schedule: EVERY_DAY_AT_MIDNIGHT (0 0 * * *)
  @Post('/update-search-client-dev')
  @UseGuards(InternalTokenGuard)
  @HttpCode(HttpStatus.OK)
  updateSearchClient() {
    if (process.env.NODE_ENV === Environments.PRODUCTION) {
      return this.logger.log(
        `It should run updateSearchClient, but you are in ${process.env.NODE_ENV} env`,
      );
    }

    return this.updateSearchClientService.handler();
  }
}
