import { MiddlewareConsumer, Module, NestModule, RequestMethod } from '@nestjs/common';
import { DatabaseModule } from './infrastructure/database/database.module';
import { ConfigModule } from './infrastructure/config/config.module';
import { CornerStoreModule } from './application/corner-store/corner-store.module';
import { TokenValidationMiddleware } from './middlewares/token';
import { AccountModule } from './application/account/account.module';
import { WhatsappModule } from './infrastructure/external-services/whatsapp/whatsapp.module';
import { TransactionModule } from './application/transaction/transaction.module';
import { WholesalerModule } from './application/wholesaler/wholesaler.module';
import { BotModule } from './application/bot/bot.module';
import { GoogleMapsModule } from './infrastructure/external-services/google-maps/google-maps.module';
import { AuthModule } from './authentication/auth.module';
import { CustomerAuthModule } from './application/customer-auth/customer-auth.module';
import { OpenpayModule } from './infrastructure/external-services/openpay/openpay.module';
import { CustomerTransactionModule } from './application/customer-transaction/customer-transaction.module';
import { AirtableModule } from './infrastructure/external-services/airtable/airtable.module';
import { ScheduleModule } from './infrastructure/schedule/schedule.module';
import { CronTransactionModule } from './application/cron-transaction/cron-transaction.module';
import { SlackModule } from './infrastructure/external-services/slack/slack.module';
import { BackofficeModule } from './application/backoffice/backoffice.module';
import { EventEmitterModule } from './infrastructure/event-emitter/event-emitter.module';
import { CronNotificationModule } from './application/cron-notification/cron-notification.module';
import { ConektaModule } from './infrastructure/external-services/conekta/conekta.module';
import { FeatureFlagModule } from './infrastructure/feature-flag/feature-flag.module';
import { InvoiceProviderModule } from './infrastructure/external-services/invoice/invoice.module';
import { PaymentPlanModule } from './application/payment-plan/payment-plan.module';
import { InvoiceModule } from './application/invoice/invoice.module';
import { DataApiModule } from './infrastructure/external-services/data-api/data-api.module';
import { CronCornerStoreModule } from './application/cron-corner-store/cron-corner-store.module';
import { CronRevenueShareModule } from './application/cron-revenue-share/cron-revenue-share.module';
import { BitsoModule } from './infrastructure/external-services/bitso/bitso.module';
import { AlgoliaModule } from './infrastructure/external-services/algolia/algolia.module';
import { MakeModule } from './infrastructure/external-services/make/make.module';
import { DatalakeModule } from './application/datalake/datalake.module';
import { LinkModule } from './application/link/link.module';
import { SmsModule } from './infrastructure/external-services/sms/sms.module';
import { ZapsignModule } from './infrastructure/external-services/zapsign/zapsign.module';
import { ChiperApiModule } from './infrastructure/external-services/chiper-api/chiper-api.module';
import { TelemetryModule } from './infrastructure/telemetry/telemetry.module';
import { LoggingModule } from './infrastructure/logging/logging.module';
import { TrullyModule } from './infrastructure/external-services/trully/trully.module';
import { HttpModule } from './infrastructure/http/http.module';
import { RateLimitModule } from './infrastructure/rate-limit/rate-limit.module';
import { NetpayModule } from './infrastructure/external-services/netpay/netpay.module';
import { QueueModule } from './infrastructure/queue/queue.module';
import { HeladosHolandaModule } from './infrastructure/wholesaler/wholesalers/helados-holanda/helados-holanda.module';
import { CollectionModule } from './application/collection/collection.module';

@Module({
  imports: [
    DatabaseModule,
    ConfigModule,
    CornerStoreModule,
    AccountModule,
    WhatsappModule,
    TransactionModule,
    WholesalerModule,
    BotModule,
    GoogleMapsModule,
    AuthModule,
    CustomerAuthModule,
    OpenpayModule,
    ConektaModule,
    CustomerTransactionModule,
    AirtableModule,
    ScheduleModule,
    CronTransactionModule,
    SlackModule,
    BackofficeModule,
    EventEmitterModule,
    CronNotificationModule,
    InvoiceProviderModule,
    FeatureFlagModule,
    PaymentPlanModule,
    InvoiceModule,
    DataApiModule,
    CronCornerStoreModule,
    CronRevenueShareModule,
    BitsoModule,
    AlgoliaModule,
    MakeModule,
    DatalakeModule,
    LinkModule,
    SmsModule,
    ZapsignModule,
    ChiperApiModule,
    TelemetryModule,
    LoggingModule,
    TrullyModule,
    HttpModule,
    RateLimitModule,
    NetpayModule,
    QueueModule,
    HeladosHolandaModule,
    CollectionModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(TokenValidationMiddleware)
      .exclude(
        { path: 'bot', method: RequestMethod.POST },
        'customer/auth/(.*)',
        { path: 'corner-store/', method: RequestMethod.GET },
        { path: 'corner-store/transaction/(.*)', method: RequestMethod.GET },
        { path: 'corner-store/pending-transactions', method: RequestMethod.GET },
        'customer-transaction/(.*)',
        { path: 'backoffice', method: RequestMethod.POST },
        'payment-plan/(.*)',
        { path: 'account/contract/notification', method: RequestMethod.POST },
        'collection/(.*)',
        'image/(.*)',
      )
      .forRoutes('*');
  }
}
